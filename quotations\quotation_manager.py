#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام عروض الأسعار المكتمل لتطبيق تصميم الأثاث
Complete Quotation Management System for Furniture Design Application
"""

import os
import json
import sqlite3
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QDialog, QFormLayout, QDialogButtonBox, QGroupBox,
    QCheckBox, QDoubleSpinBox, QTabWidget, QSplitter,
    QTreeWidget, Q<PERSON>reeWidgetItem, QProgressBar, Q<PERSON>rame
)
from PyQt6.QtCore import Qt, QD<PERSON>, <PERSON><PERSON><PERSON><PERSON>, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QPixmap, QPainter
from PyQt6.QtPrintSupport import QPrinter, QPrintDialog

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class QuotationItem:
    """عنصر في عرض السعر"""
    id: int
    quotation_id: int
    item_name: str
    description: str
    quantity: float
    unit: str
    unit_price: float
    discount_percent: float
    total_price: float
    notes: str


@dataclass
class Quotation:
    """عرض السعر"""
    id: int
    quotation_number: str
    client_id: int
    client_name: str
    title: str
    description: str
    quotation_date: str
    valid_until: str
    status: str  # draft, sent, accepted, rejected, expired
    subtotal: float
    discount_amount: float
    tax_amount: float
    total_amount: float
    terms_conditions: str
    notes: str
    created_by: str
    created_date: str
    last_modified: str
    
    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
        if not self.last_modified:
            self.last_modified = datetime.now().isoformat()


class QuotationDialog(QDialog):
    """نافذة إنشاء/تعديل عرض السعر"""
    
    def __init__(self, quotation: Optional[Quotation] = None, parent=None):
        super().__init__(parent)
        self.quotation = quotation
        self.is_edit_mode = quotation is not None
        self.items = []
        self.init_ui()
        if self.quotation:
            self.load_quotation_data()
            
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تعديل عرض السعر" if self.is_edit_mode else "إنشاء عرض سعر جديد")
        self.setModal(True)
        self.resize(800, 700)
        
        layout = QVBoxLayout(self)
        
        # تبويبات البيانات
        tabs = QTabWidget()
        
        # تبويب البيانات الأساسية
        basic_tab = self.create_basic_info_tab()
        tabs.addTab(basic_tab, "البيانات الأساسية")
        
        # تبويب العناصر
        items_tab = self.create_items_tab()
        tabs.addTab(items_tab, "العناصر")
        
        # تبويب الشروط والأحكام
        terms_tab = self.create_terms_tab()
        tabs.addTab(terms_tab, "الشروط والأحكام")
        
        layout.addWidget(tabs)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setIcon(IconsManager.get_standard_icon('save'))
        self.save_btn.clicked.connect(self.accept)
        
        self.preview_btn = QPushButton("معاينة")
        self.preview_btn.setIcon(IconsManager.get_standard_icon('preview'))
        self.preview_btn.clicked.connect(self.preview_quotation)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setIcon(IconsManager.get_standard_icon('close'))
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.preview_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
        
    def create_basic_info_tab(self):
        """إنشاء تبويب البيانات الأساسية"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(ModernStyleManager.SPACING['md'])
        
        # رقم عرض السعر
        self.quotation_number_edit = QLineEdit()
        self.quotation_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        self.quotation_number_edit.setReadOnly(True)
        layout.addRow("رقم عرض السعر:", self.quotation_number_edit)
        
        # العميل
        self.client_combo = QComboBox()
        self.client_combo.setEditable(True)
        self.client_combo.setPlaceholderText("اختر العميل")
        layout.addRow("العميل *:", self.client_combo)
        
        # العنوان
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("عنوان عرض السعر")
        layout.addRow("العنوان *:", self.title_edit)
        
        # الوصف
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("وصف مختصر لعرض السعر")
        layout.addRow("الوصف:", self.description_edit)
        
        # تاريخ عرض السعر
        self.quotation_date_edit = QDateEdit()
        self.quotation_date_edit.setDate(QDate.currentDate())
        self.quotation_date_edit.setCalendarPopup(True)
        layout.addRow("تاريخ عرض السعر:", self.quotation_date_edit)
        
        # صالح حتى
        self.valid_until_edit = QDateEdit()
        self.valid_until_edit.setDate(QDate.currentDate().addDays(30))
        self.valid_until_edit.setCalendarPopup(True)
        layout.addRow("صالح حتى:", self.valid_until_edit)
        
        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["مسودة", "مرسل", "مقبول", "مرفوض", "منتهي الصلاحية"])
        layout.addRow("الحالة:", self.status_combo)
        
        return tab
        
    def create_items_tab(self):
        """إنشاء تبويب العناصر"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # شريط أدوات العناصر
        items_toolbar = QHBoxLayout()
        
        add_item_btn = QPushButton("إضافة عنصر")
        add_item_btn.setIcon(IconsManager.get_standard_icon('add'))
        add_item_btn.clicked.connect(self.add_item)
        
        edit_item_btn = QPushButton("تعديل")
        edit_item_btn.setIcon(IconsManager.get_standard_icon('edit'))
        edit_item_btn.clicked.connect(self.edit_item)
        
        delete_item_btn = QPushButton("حذف")
        delete_item_btn.setIcon(IconsManager.get_standard_icon('delete'))
        delete_item_btn.clicked.connect(self.delete_item)
        
        items_toolbar.addWidget(add_item_btn)
        items_toolbar.addWidget(edit_item_btn)
        items_toolbar.addWidget(delete_item_btn)
        items_toolbar.addStretch()
        
        layout.addLayout(items_toolbar)
        
        # جدول العناصر
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "العنصر", "الوصف", "الكمية", "الوحدة", "السعر", "الخصم %", "الإجمالي"
        ])
        
        # تخصيص الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # العنصر
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # الوصف
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # الكمية
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # الوحدة
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # السعر
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # الخصم
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # الإجمالي
        
        layout.addWidget(self.items_table)
        
        # ملخص الأسعار
        summary_frame = QFrame()
        summary_frame.setFrameStyle(QFrame.Shape.Box)
        summary_layout = QFormLayout(summary_frame)
        
        self.subtotal_label = QLabel("0.00 ريال")
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setRange(0, 999999)
        self.discount_spin.setSuffix(" ريال")
        self.discount_spin.valueChanged.connect(self.calculate_totals)
        
        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setRange(0, 100)
        self.tax_spin.setSuffix(" %")
        self.tax_spin.setValue(15)  # ضريبة القيمة المضافة
        self.tax_spin.valueChanged.connect(self.calculate_totals)
        
        self.total_label = QLabel("0.00 ريال")
        self.total_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
            }}
        """)
        
        summary_layout.addRow("المجموع الفرعي:", self.subtotal_label)
        summary_layout.addRow("الخصم:", self.discount_spin)
        summary_layout.addRow("الضريبة:", self.tax_spin)
        summary_layout.addRow("الإجمالي:", self.total_label)
        
        layout.addWidget(summary_frame)
        
        return tab
        
    def create_terms_tab(self):
        """إنشاء تبويب الشروط والأحكام"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # الشروط والأحكام
        terms_label = QLabel("الشروط والأحكام:")
        terms_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                margin-bottom: {ModernStyleManager.SPACING['sm']}px;
            }}
        """)
        layout.addWidget(terms_label)
        
        self.terms_edit = QTextEdit()
        self.terms_edit.setPlaceholderText("أدخل الشروط والأحكام...")
        
        # شروط افتراضية
        default_terms = """1. الأسعار المذكورة شاملة ضريبة القيمة المضافة
2. مدة التسليم: 15-30 يوم عمل من تاريخ التأكيد
3. الدفع: 50% مقدم و 50% عند التسليم
4. التركيب مجاني داخل المدينة
5. ضمان سنة واحدة ضد عيوب الصناعة
6. العرض صالح لمدة 30 يوم من تاريخ الإصدار"""
        
        self.terms_edit.setPlainText(default_terms)
        layout.addWidget(self.terms_edit)
        
        # الملاحظات
        notes_label = QLabel("ملاحظات إضافية:")
        notes_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                margin: {ModernStyleManager.SPACING['md']}px 0 {ModernStyleManager.SPACING['sm']}px 0;
            }}
        """)
        layout.addWidget(notes_label)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        layout.addWidget(self.notes_edit)
        
        return tab
        
    def add_item(self):
        """إضافة عنصر جديد"""
        # هنا يمكن إضافة نافذة لإدخال تفاصيل العنصر
        # للبساطة، سنضيف عنصر تجريبي
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)
        
        self.items_table.setItem(row, 0, QTableWidgetItem("عنصر جديد"))
        self.items_table.setItem(row, 1, QTableWidgetItem("وصف العنصر"))
        self.items_table.setItem(row, 2, QTableWidgetItem("1"))
        self.items_table.setItem(row, 3, QTableWidgetItem("قطعة"))
        self.items_table.setItem(row, 4, QTableWidgetItem("1000"))
        self.items_table.setItem(row, 5, QTableWidgetItem("0"))
        self.items_table.setItem(row, 6, QTableWidgetItem("1000"))
        
        self.calculate_totals()
        
    def edit_item(self):
        """تعديل العنصر المحدد"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            # هنا يمكن فتح نافذة تعديل العنصر
            pass
            
    def delete_item(self):
        """حذف العنصر المحدد"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا العنصر؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.items_table.removeRow(current_row)
                self.calculate_totals()
                
    def calculate_totals(self):
        """حساب الإجماليات"""
        subtotal = 0.0
        
        for row in range(self.items_table.rowCount()):
            total_item = self.items_table.item(row, 6)
            if total_item:
                try:
                    subtotal += float(total_item.text())
                except ValueError:
                    pass
                    
        self.subtotal_label.setText(f"{subtotal:,.2f} ريال")
        
        discount = self.discount_spin.value()
        tax_rate = self.tax_spin.value() / 100
        
        after_discount = subtotal - discount
        tax_amount = after_discount * tax_rate
        total = after_discount + tax_amount
        
        self.total_label.setText(f"{total:,.2f} ريال")
        
    def preview_quotation(self):
        """معاينة عرض السعر"""
        QMessageBox.information(self, "معاينة", "سيتم تطوير معاينة عرض السعر قريباً")
        
    def load_quotation_data(self):
        """تحميل بيانات عرض السعر للتعديل"""
        if not self.quotation:
            return
            
        # تحميل البيانات الأساسية
        self.quotation_number_edit.setText(self.quotation.quotation_number)
        self.title_edit.setText(self.quotation.title)
        self.description_edit.setPlainText(self.quotation.description)
        self.terms_edit.setPlainText(self.quotation.terms_conditions)
        self.notes_edit.setPlainText(self.quotation.notes)
        
        # تحميل التواريخ
        try:
            quotation_date = QDate.fromString(self.quotation.quotation_date, "yyyy-MM-dd")
            self.quotation_date_edit.setDate(quotation_date)
            
            valid_until = QDate.fromString(self.quotation.valid_until, "yyyy-MM-dd")
            self.valid_until_edit.setDate(valid_until)
        except:
            pass
            
        # تحميل الحالة
        statuses = {"draft": 0, "sent": 1, "accepted": 2, "rejected": 3, "expired": 4}
        self.status_combo.setCurrentIndex(statuses.get(self.quotation.status, 0))
        
    def get_quotation_data(self) -> Quotation:
        """الحصول على بيانات عرض السعر من النموذج"""
        statuses = ["draft", "sent", "accepted", "rejected", "expired"]
        
        return Quotation(
            id=self.quotation.id if self.quotation else 0,
            quotation_number=self.quotation_number_edit.text() or self.generate_quotation_number(),
            client_id=0,  # سيتم تحديده من قاعدة البيانات
            client_name=self.client_combo.currentText(),
            title=self.title_edit.text().strip(),
            description=self.description_edit.toPlainText().strip(),
            quotation_date=self.quotation_date_edit.date().toString("yyyy-MM-dd"),
            valid_until=self.valid_until_edit.date().toString("yyyy-MM-dd"),
            status=statuses[self.status_combo.currentIndex()],
            subtotal=float(self.subtotal_label.text().replace(" ريال", "").replace(",", "")),
            discount_amount=self.discount_spin.value(),
            tax_amount=0.0,  # سيتم حسابه
            total_amount=float(self.total_label.text().replace(" ريال", "").replace(",", "")),
            terms_conditions=self.terms_edit.toPlainText().strip(),
            notes=self.notes_edit.toPlainText().strip(),
            created_by="المستخدم الحالي",
            created_date=self.quotation.created_date if self.quotation else datetime.now().isoformat(),
            last_modified=datetime.now().isoformat()
        )
        
    def generate_quotation_number(self) -> str:
        """إنشاء رقم عرض سعر تلقائي"""
        today = datetime.now()
        return f"Q-{today.year}{today.month:02d}{today.day:02d}-{today.hour:02d}{today.minute:02d}"
        
    def validate_data(self) -> bool:
        """التحقق من صحة البيانات"""
        if not self.title_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال عنوان عرض السعر")
            self.title_edit.setFocus()
            return False
            
        if not self.client_combo.currentText().strip():
            QMessageBox.warning(self, "خطأ", "يرجى اختيار العميل")
            self.client_combo.setFocus()
            return False
            
        if self.items_table.rowCount() == 0:
            QMessageBox.warning(self, "خطأ", "يرجى إضافة عنصر واحد على الأقل")
            return False
            
        return True
        
    def accept(self):
        """قبول النموذج"""
        if self.validate_data():
            super().accept()
