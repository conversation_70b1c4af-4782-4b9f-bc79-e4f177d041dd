#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معاين ثلاثي الأبعاد متقدم لنماذج الأثاث
Advanced 3D Viewer for Furniture Models
"""

import os
import numpy as np
import trimesh
from typing import List, Dict, Any, Optional, Tuple
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QSlider, QComboBox, QCheckBox, QGroupBox, QFileDialog,
    QMessageBox, QProgressBar, QSplitter, QTextEdit, QSpinBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor
from PyQt6.QtOpenGLWidgets import QOpenGLWidget
from PyQt6.QtOpenGL import QOpenGLVersionProfile

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    from mpl_toolkits.mplot3d import Axes3D
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


class Model3DInfo:
    """معلومات النموذج ثلاثي الأبعاد"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.file_name = os.path.basename(file_path)
        self.file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        self.format = os.path.splitext(file_path)[1].lower()
        self.mesh = None
        self.components = []
        self.bounds = None
        self.volume = 0.0
        self.surface_area = 0.0
        self.vertices_count = 0
        self.faces_count = 0
        self.is_loaded = False
        self.load_error = None


class ModelLoader(QThread):
    """خيط تحميل النماذج ثلاثية الأبعاد"""
    
    progress_updated = pyqtSignal(int, str)
    model_loaded = pyqtSignal(object)  # Model3DInfo
    loading_failed = pyqtSignal(str)
    
    def __init__(self, file_path: str):
        super().__init__()
        self.file_path = file_path
        
    def run(self):
        """تشغيل عملية التحميل"""
        try:
            self.progress_updated.emit(10, "بدء تحميل النموذج...")
            
            # إنشاء كائن معلومات النموذج
            model_info = Model3DInfo(self.file_path)
            
            self.progress_updated.emit(30, "قراءة الملف...")
            
            # تحميل النموذج باستخدام trimesh
            mesh = trimesh.load(self.file_path)
            model_info.mesh = mesh
            
            self.progress_updated.emit(50, "تحليل النموذج...")
            
            # تحليل النموذج
            if isinstance(mesh, trimesh.Scene):
                # نموذج متعدد المكونات
                model_info.components = list(mesh.geometry.keys())
                model_info.bounds = mesh.bounds if hasattr(mesh, 'bounds') else None
                model_info.volume = sum(geom.volume for geom in mesh.geometry.values() 
                                      if hasattr(geom, 'volume'))
                model_info.surface_area = sum(geom.area for geom in mesh.geometry.values() 
                                            if hasattr(geom, 'area'))
                model_info.vertices_count = sum(len(geom.vertices) for geom in mesh.geometry.values() 
                                              if hasattr(geom, 'vertices'))
                model_info.faces_count = sum(len(geom.faces) for geom in mesh.geometry.values() 
                                           if hasattr(geom, 'faces'))
            else:
                # نموذج مكون واحد
                model_info.components = ["المكون الرئيسي"]
                model_info.bounds = mesh.bounds
                model_info.volume = float(mesh.volume) if hasattr(mesh, 'volume') else 0.0
                model_info.surface_area = float(mesh.area) if hasattr(mesh, 'area') else 0.0
                model_info.vertices_count = len(mesh.vertices) if hasattr(mesh, 'vertices') else 0
                model_info.faces_count = len(mesh.faces) if hasattr(mesh, 'faces') else 0
            
            self.progress_updated.emit(80, "إنهاء التحليل...")
            
            model_info.is_loaded = True
            
            self.progress_updated.emit(100, "تم التحميل بنجاح!")
            self.model_loaded.emit(model_info)
            
        except Exception as e:
            error_msg = f"خطأ في تحميل النموذج: {str(e)}"
            self.loading_failed.emit(error_msg)


class Advanced3DViewer(QWidget):
    """معاين ثلاثي الأبعاد متقدم"""
    
    model_selected = pyqtSignal(object)  # Model3DInfo
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_model = None
        self.supported_formats = ['.obj', '.stl', '.dae', '.ply', '.off', '.3mf']
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # شريط الأدوات العلوي
        self.create_toolbar(layout)
        
        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # منطقة العرض
        self.create_viewer_area(main_splitter)
        
        # لوحة التحكم
        self.create_control_panel(main_splitter)
        
        # تعيين النسب
        main_splitter.setSizes([700, 300])
        
        layout.addWidget(main_splitter)
        
    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)
        
        # زر تحميل نموذج
        load_btn = QPushButton("تحميل نموذج")
        load_btn.setIcon(IconsManager.get_standard_icon('open'))
        load_btn.clicked.connect(self.load_model)
        
        # زر حفظ الصورة
        save_image_btn = QPushButton("حفظ صورة")
        save_image_btn.setIcon(IconsManager.get_standard_icon('save'))
        save_image_btn.clicked.connect(self.save_screenshot)
        
        # زر إعادة تعيين العرض
        reset_view_btn = QPushButton("إعادة تعيين العرض")
        reset_view_btn.setIcon(IconsManager.get_standard_icon('refresh'))
        reset_view_btn.clicked.connect(self.reset_view)
        
        # معلومات النموذج الحالي
        self.model_info_label = QLabel("لا يوجد نموذج محمل")
        self.model_info_label.setStyleSheet(f"""
            QLabel {{
                background-color: {ModernStyleManager.COLORS['card']};
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }}
        """)
        
        toolbar_layout.addWidget(load_btn)
        toolbar_layout.addWidget(save_image_btn)
        toolbar_layout.addWidget(reset_view_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.model_info_label)
        
        parent_layout.addWidget(toolbar_widget)
        
    def create_viewer_area(self, parent_splitter):
        """إنشاء منطقة العرض"""
        viewer_widget = QWidget()
        viewer_layout = QVBoxLayout(viewer_widget)
        viewer_layout.setContentsMargins(4, 4, 4, 4)
        
        # عنوان منطقة العرض
        title_label = QLabel("معاين ثلاثي الأبعاد")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                padding: 8px;
                background-color: {ModernStyleManager.COLORS['primary']};
                color: {ModernStyleManager.COLORS['text_light']};
                border-radius: 6px;
            }}
        """)
        
        # منطقة العرض الفعلية
        if MATPLOTLIB_AVAILABLE:
            self.create_matplotlib_viewer(viewer_layout)
        else:
            self.create_fallback_viewer(viewer_layout)
        
        # شريط التقدم للتحميل
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {ModernStyleManager.COLORS['border']};
                border-radius: 5px;
                text-align: center;
                background-color: {ModernStyleManager.COLORS['surface']};
            }}
            QProgressBar::chunk {{
                background-color: {ModernStyleManager.COLORS['secondary']};
                border-radius: 3px;
            }}
        """)
        
        viewer_layout.addWidget(title_label)
        viewer_layout.addWidget(self.progress_bar)
        
        parent_splitter.addWidget(viewer_widget)
        
    def create_matplotlib_viewer(self, parent_layout):
        """إنشاء معاين matplotlib"""
        # إنشاء الشكل
        self.figure = Figure(figsize=(8, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setStyleSheet(f"""
            background-color: {ModernStyleManager.COLORS['surface']};
            border: 1px solid {ModernStyleManager.COLORS['border']};
            border-radius: 8px;
        """)
        
        # إنشاء المحور ثلاثي الأبعاد
        self.ax = self.figure.add_subplot(111, projection='3d')
        self.ax.set_title("معاين النماذج ثلاثية الأبعاد", fontsize=14, fontweight='bold')
        
        # إعداد المحور
        self.setup_3d_axis()
        
        parent_layout.addWidget(self.canvas)
        
    def create_fallback_viewer(self, parent_layout):
        """إنشاء معاين بديل"""
        fallback_label = QLabel("معاين ثلاثي الأبعاد غير متاح\nيرجى تثبيت matplotlib")
        fallback_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        fallback_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                color: {ModernStyleManager.COLORS['text_muted']};
                background-color: {ModernStyleManager.COLORS['card']};
                border: 2px dashed {ModernStyleManager.COLORS['border']};
                border-radius: 8px;
                padding: 50px;
            }}
        """)
        
        parent_layout.addWidget(fallback_label)
        
    def create_control_panel(self, parent_splitter):
        """إنشاء لوحة التحكم"""
        control_widget = QWidget()
        control_widget.setMaximumWidth(350)
        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(4, 4, 4, 4)
        
        # عنوان لوحة التحكم
        title_label = QLabel("لوحة التحكم")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                padding: 8px;
                background-color: {ModernStyleManager.COLORS['secondary']};
                color: {ModernStyleManager.COLORS['text_light']};
                border-radius: 6px;
                margin-bottom: 8px;
            }}
        """)
        
        # مجموعة معلومات النموذج
        self.create_model_info_group(control_layout)
        
        # مجموعة إعدادات العرض
        self.create_view_settings_group(control_layout)
        
        # مجموعة التحليل
        self.create_analysis_group(control_layout)
        
        control_layout.addWidget(title_label)
        control_layout.addStretch()
        
        parent_splitter.addWidget(control_widget)
        
    def create_model_info_group(self, parent_layout):
        """إنشاء مجموعة معلومات النموذج"""
        info_group = QGroupBox("معلومات النموذج")
        info_layout = QVBoxLayout(info_group)
        
        # معلومات الملف
        self.file_name_label = QLabel("الملف: غير محدد")
        self.file_size_label = QLabel("الحجم: غير محدد")
        self.format_label = QLabel("الصيغة: غير محددة")
        
        # معلومات النموذج
        self.vertices_label = QLabel("الرؤوس: 0")
        self.faces_label = QLabel("الوجوه: 0")
        self.volume_label = QLabel("الحجم: 0")
        self.area_label = QLabel("المساحة: 0")
        
        # إضافة التسميات
        for label in [self.file_name_label, self.file_size_label, self.format_label,
                     self.vertices_label, self.faces_label, self.volume_label, self.area_label]:
            label.setStyleSheet("QLabel { padding: 4px; }")
            info_layout.addWidget(label)
        
        parent_layout.addWidget(info_group)
        
    def create_view_settings_group(self, parent_layout):
        """إنشاء مجموعة إعدادات العرض"""
        view_group = QGroupBox("إعدادات العرض")
        view_layout = QVBoxLayout(view_group)
        
        # خيارات العرض
        self.wireframe_check = QCheckBox("عرض الإطار السلكي")
        self.axes_check = QCheckBox("عرض المحاور")
        self.axes_check.setChecked(True)
        
        # ألوان العرض
        color_label = QLabel("لون النموذج:")
        self.color_combo = QComboBox()
        self.color_combo.addItems(["أزرق", "أحمر", "أخضر", "رمادي", "ذهبي"])
        
        view_layout.addWidget(self.wireframe_check)
        view_layout.addWidget(self.axes_check)
        view_layout.addWidget(color_label)
        view_layout.addWidget(self.color_combo)
        
        parent_layout.addWidget(view_group)
        
    def create_analysis_group(self, parent_layout):
        """إنشاء مجموعة التحليل"""
        analysis_group = QGroupBox("تحليل النموذج")
        analysis_layout = QVBoxLayout(analysis_group)
        
        # أزرار التحليل
        analyze_btn = QPushButton("تحليل الأبعاد")
        analyze_btn.setIcon(IconsManager.get_standard_icon('ruler'))
        analyze_btn.clicked.connect(self.analyze_dimensions)
        
        export_data_btn = QPushButton("تصدير البيانات")
        export_data_btn.setIcon(IconsManager.get_standard_icon('export'))
        export_data_btn.clicked.connect(self.export_model_data)
        
        analysis_layout.addWidget(analyze_btn)
        analysis_layout.addWidget(export_data_btn)
        
        parent_layout.addWidget(analysis_group)
        
    def setup_3d_axis(self):
        """إعداد المحور ثلاثي الأبعاد"""
        if not MATPLOTLIB_AVAILABLE:
            return
            
        self.ax.clear()
        self.ax.set_xlabel('X (مم)')
        self.ax.set_ylabel('Y (مم)')
        self.ax.set_zlabel('Z (مم)')
        
        # إعداد الشبكة
        self.ax.grid(True, alpha=0.3)
        
        # إعداد الخلفية
        self.ax.xaxis.pane.fill = False
        self.ax.yaxis.pane.fill = False
        self.ax.zaxis.pane.fill = False
        
        self.canvas.draw()
        
    def load_model(self):
        """تحميل نموذج ثلاثي الأبعاد"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "اختر نموذج ثلاثي الأبعاد",
            "",
            "نماذج ثلاثية الأبعاد (*.obj *.stl *.dae *.ply *.off *.3mf);;جميع الملفات (*)"
        )
        
        if file_path:
            self.load_model_from_path(file_path)
            
    def load_model_from_path(self, file_path: str):
        """تحميل نموذج من مسار محدد"""
        # إظهار شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # إنشاء خيط التحميل
        self.loader_thread = ModelLoader(file_path)
        self.loader_thread.progress_updated.connect(self.update_loading_progress)
        self.loader_thread.model_loaded.connect(self.on_model_loaded)
        self.loader_thread.loading_failed.connect(self.on_loading_failed)
        self.loader_thread.start()
        
    def update_loading_progress(self, value: int, message: str):
        """تحديث تقدم التحميل"""
        self.progress_bar.setValue(value)
        self.model_info_label.setText(message)
        
    def on_model_loaded(self, model_info: Model3DInfo):
        """عند تحميل النموذج بنجاح"""
        self.current_model = model_info
        self.progress_bar.setVisible(False)
        
        # تحديث معلومات النموذج
        self.update_model_info()
        
        # عرض النموذج
        self.display_model()
        
        # إرسال إشارة
        self.model_selected.emit(model_info)
        
    def on_loading_failed(self, error_message: str):
        """عند فشل التحميل"""
        self.progress_bar.setVisible(False)
        self.model_info_label.setText("فشل التحميل")
        
        QMessageBox.critical(self, "خطأ في التحميل", error_message)
        
    def update_model_info(self):
        """تحديث معلومات النموذج"""
        if not self.current_model:
            return
            
        model = self.current_model
        
        # معلومات الملف
        self.file_name_label.setText(f"الملف: {model.file_name}")
        self.file_size_label.setText(f"الحجم: {model.file_size / 1024:.1f} KB")
        self.format_label.setText(f"الصيغة: {model.format.upper()}")
        
        # معلومات النموذج
        self.vertices_label.setText(f"الرؤوس: {model.vertices_count:,}")
        self.faces_label.setText(f"الوجوه: {model.faces_count:,}")
        self.volume_label.setText(f"الحجم: {model.volume:.2f} مم³")
        self.area_label.setText(f"المساحة: {model.surface_area:.2f} مم²")
        
        # تحديث شريط المعلومات
        self.model_info_label.setText(f"تم تحميل: {model.file_name}")
        
    def display_model(self):
        """عرض النموذج"""
        if not MATPLOTLIB_AVAILABLE or not self.current_model:
            return
            
        try:
            mesh = self.current_model.mesh
            
            self.ax.clear()
            
            if isinstance(mesh, trimesh.Scene):
                # عرض مشهد متعدد المكونات
                for name, geometry in mesh.geometry.items():
                    if hasattr(geometry, 'vertices') and hasattr(geometry, 'faces'):
                        self.plot_mesh(geometry)
            else:
                # عرض مكون واحد
                self.plot_mesh(mesh)
            
            # إعداد المحور
            self.setup_3d_axis()
            
            # تحديث العرض
            self.canvas.draw()
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ في العرض", f"لا يمكن عرض النموذج: {str(e)}")
            
    def plot_mesh(self, mesh):
        """رسم شبكة ثلاثية الأبعاد"""
        if not hasattr(mesh, 'vertices') or not hasattr(mesh, 'faces'):
            return
            
        vertices = mesh.vertices
        faces = mesh.faces
        
        # رسم الوجوه
        for face in faces:
            triangle = vertices[face]
            self.ax.plot_trisurf(
                triangle[:, 0], triangle[:, 1], triangle[:, 2],
                alpha=0.7, shade=True
            )
            
    def reset_view(self):
        """إعادة تعيين العرض"""
        if MATPLOTLIB_AVAILABLE:
            self.setup_3d_axis()
            if self.current_model:
                self.display_model()
                
    def save_screenshot(self):
        """حفظ لقطة شاشة"""
        if not MATPLOTLIB_AVAILABLE or not self.current_model:
            QMessageBox.warning(self, "تحذير", "لا يوجد نموذج لحفظه")
            return
            
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getSaveFileName(
            self,
            "حفظ صورة النموذج",
            f"{self.current_model.file_name}_screenshot.png",
            "صور PNG (*.png);;صور JPEG (*.jpg);;جميع الملفات (*)"
        )
        
        if file_path:
            self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
            QMessageBox.information(self, "نجح الحفظ", f"تم حفظ الصورة في:\n{file_path}")
            
    def analyze_dimensions(self):
        """تحليل أبعاد النموذج"""
        if not self.current_model:
            QMessageBox.warning(self, "تحذير", "لا يوجد نموذج للتحليل")
            return
            
        # عرض نافذة تحليل الأبعاد
        QMessageBox.information(self, "تحليل الأبعاد", "سيتم تنفيذ تحليل الأبعاد قريباً")
        
    def export_model_data(self):
        """تصدير بيانات النموذج"""
        if not self.current_model:
            QMessageBox.warning(self, "تحذير", "لا يوجد نموذج للتصدير")
            return
            
        # عرض نافذة تصدير البيانات
        QMessageBox.information(self, "تصدير البيانات", "سيتم تنفيذ تصدير البيانات قريباً")
