# 🪑 مصمم الأثاث الاحترافي - Professional Furniture Designer

## الإصدار 3.0 - PyQt6 Edition

تطبيق شامل ومتقدم لتصميم الأثاث وإدارة مصانع الأثاث، مطور باستخدام PyQt6 مع واجهة مستخدم حديثة ومهنية.

## 🌟 الميزات الرئيسية

### 🎨 واجهة مستخدم حديثة
- تصميم عصري ومهني باستخدام PyQt6
- نظام ألوان متناسق ومريح للعين
- أيقونات عالية الجودة وتأثيرات بصرية حديثة
- تخطيط محسن لتجربة مستخدم أفضل

### 🔧 معاينة ثلاثية الأبعاد متقدمة
- دعم متعدد الصيغ: `.obj`, `.stl`, `.dae`, `.ply`, `.off`, `.3mf`
- معاينة تفاعلية مع إمكانية التكبير والتصغير
- تحليل أبعاد النماذج والحجم والمساحة
- حفظ لقطات شاشة عالية الجودة

### 📁 نظام السحب والإفلات
- سحب وإفلات الملفات مباشرة إلى التطبيق
- معالجة متعددة الخيوط للملفات الكبيرة
- دعم معاينة فورية للنماذج ثلاثية الأبعاد
- فلترة تلقائية للملفات المدعومة

### 🏭 تكامل آلات CNC
- إدارة شاملة لآلات CNC المختلفة
- دعم اتصالات متعددة: Serial, Ethernet, USB
- مراقبة حية لحالة الآلات
- إدارة مهام التصنيع والجدولة
- أوامر طوارئ وتحكم سريع

### 📊 نظام التقارير المتقدم
- تقارير مالية شاملة ومفصلة
- تقارير المخزون مع تتبع الحركات
- تقارير العملاء والمشاريع
- تصدير متعدد الصيغ: PDF, Excel, CSV, HTML
- رسوم بيانية تفاعلية ومخططات

### 💾 نظام النسخ الاحتياطي الذكي
- نسخ احتياطي تلقائي ومجدول
- أنواع متعددة: كامل، تدريجي، تفاضلي
- ضغط وتشفير البيانات
- استعادة سريعة وآمنة
- إدارة دورة حياة النسخ الاحتياطية

## 🛠️ المتطلبات التقنية

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM (8 GB مُوصى به)
- **التخزين**: 2 GB مساحة فارغة
- **كرت الرسوميات**: دعم OpenGL 3.3+ (للمعاينة ثلاثية الأبعاد)

### المكتبات المطلوبة
```
PyQt6>=6.4.0
trimesh>=4.0.0
numpy>=1.20.0
pandas>=1.3.0
openpyxl>=3.0.0
matplotlib>=3.5.0
```

### المكتبات الاختيارية (لميزات إضافية)
```
reportlab>=3.6.0          # تصدير PDF متقدم
arabic-reshaper>=3.0.0    # دعم النصوص العربية
python-bidi>=0.4.0        # دعم الكتابة ثنائية الاتجاه
seaborn>=0.11.0           # رسوم بيانية متقدمة
scipy>=1.7.0              # حسابات علمية
```

---

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/professional-furniture-designer.git
cd professional-furniture-designer
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python run_professional_furniture_designer.py
```

أو استخدام المشغل المتقدم:
```bash
python main_application.py
```

## هيكل المشروع

```
cutlist_app/
├── main.py                     # ملف التشغيل الرئيسي
├── requirements.txt            # المكتبات المطلوبة
├── README.md                   # توثيق المشروع
├── models/                     # وحدة تحليل النماذج ثلاثية الأبعاد
│   ├── __init__.py
│   └── model_reader.py
├── cutlist/                    # وحدة توليد جدول القطع
│   ├── __init__.py
│   └── cutlist_generator.py
├── materials/                  # إدارة المواد (قادم)
├── export/                     # تصدير Excel و PDF (قادم)
├── ui/                         # ملفات الواجهة (قادم)
└── assets/                     # الأيقونات والملفات المساعدة
```

## كيفية الاستخدام

### الإصدار المبسط (main_simple.py)

#### 1. إضافة مكونات يدوياً
- انقر على زر "إضافة مكون يدوياً"
- أدخل اسم المكون والأبعاد (الطول × العرض × السماكة)
- اختر نوع المادة والكمية
- انقر "موافق" لإضافة المكون للجدول

#### 2. فتح ملف ثلاثي الأبعاد (تجريبي)
- انقر على زر "فتح ملف ثلاثي الأبعاد"
- اختر أي ملف بصيغة .obj, .stl, أو .dae
- سيتم إضافة مكونات تجريبية للجدول

#### 3. إدارة الجدول
- يمكن مراجعة جميع المكونات في الجدول
- استخدم زر "مسح الجدول" لحذف جميع البيانات
- البيانات تظهر بالتنسيق: الطول × العرض × السماكة بالمليمتر

### الإصدار الكامل (main.py) - يتطلب مكتبات إضافية

#### 1. فتح ملف ثلاثي الأبعاد
- انقر على زر "فتح ملف ثلاثي الأبعاد"
- اختر ملف بصيغة .obj, .stl, أو .dae
- انتظر حتى يكتمل التحليل التلقائي

#### 2. مراجعة النتائج
- ستظهر المكونات في الجدول مع أبعادها الحقيقية
- يتم تجميع المكونات المتشابهة تلقائياً
- يمكن تعديل نوع المادة لكل مكون

#### 3. التصدير (قادم)
- تصدير إلى Excel للتعديل المتقدم
- تصدير إلى PDF للطباعة
- حفظ المشروع للعمل عليه لاحقاً

## المكتبات المستخدمة

| المكتبة | الغرض |
|---------|--------|
| PyQt5 | الواجهة الرسومية |
| trimesh | تحليل النماذج ثلاثية الأبعاد |
| pandas | تنظيم وإدارة البيانات |
| numpy | العمليات الرياضية |
| openpyxl | تصدير Excel |
| reportlab | تصدير PDF |
| arabic-reshaper | دعم النصوص العربية |

## ✅ الميزات الجديدة في الإصدار 2.0

### تم إضافتها حديثاً
- ✅ **تصدير إلى Excel و PDF** - تصدير احترافي مع تنسيق متقدم
- ✅ **إدارة متقدمة للمواد** - قاعدة بيانات شاملة للمواد
- ✅ **حفظ وتحميل المشاريع** - نظام إدارة مشاريع كامل
- ✅ **واجهة متقدمة** - تبويبات وإحصائيات مفصلة
- ✅ **حساب التكلفة والوزن** - تقديرات دقيقة للمواد
- ✅ **تحرير مباشر** - تعديل البيانات في الجدول مباشرة
- ✅ **إحصائيات شاملة** - تحليل مفصل للمشروع

### الميزات القادمة

#### المرحلة التالية
- [ ] مدير المواد المرئي - واجهة لإضافة وتعديل المواد
- [ ] تحسين خطة القطع - خوارزميات تحسين الهدر
- [ ] رسوم بيانية - مخططات للإحصائيات
- [ ] طباعة ملصقات - ملصقات للمكونات مع باركود

#### المراحل المستقبلية
- [ ] دعم صيغ إضافية (.3ds, .fbx, .step)
- [ ] واجهة ثنائية اللغة (عربي/إنجليزي)
- [ ] تكامل مع قواعد بيانات المواد عبر الإنترنت
- [ ] تصدير إلى ماكينات CNC
- [ ] تطبيق ويب مصاحب
- [ ] API للتكامل مع برامج أخرى

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات مع اختبارات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- افتح Issue في GitHub
- راسلنا على البريد الإلكتروني
- راجع الوثائق في Wiki

---

**ملاحظة**: هذا المشروع في مرحلة التطوير النشط. بعض الميزات قد تكون غير مكتملة.
