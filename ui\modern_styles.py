#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الأنماط الحديثة لتطبيق تصميم الأثاث الاحترافي
Modern Styles System for Professional Furniture Design Application
"""

from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QPalette, QColor
from typing import Dict, Any


class ModernStyleManager:
    """مدير الأنماط الحديثة"""

    # ألوان النظام الاحترافي
    COLORS = {
        # الألوان الأساسية
        'primary': '#2C3E50',           # أزرق داكن أنيق
        'primary_light': '#34495E',     # أزرق داكن فاتح
        'secondary': '#3498DB',         # أزرق فاتح
        'accent': '#E74C3C',           # أحمر للتنبيهات
        'success': '#27AE60',          # أخضر للنجاح
        'warning': '#F39C12',          # برتقالي للتحذير
        'info': '#3498DB',             # أزرق للمعلومات

        # ألوان الخلفية
        'background': '#ECF0F1',       # رمادي فاتح جداً
        'surface': '#FFFFFF',          # أبيض
        'card': '#FAFAFA',            # رمادي فاتح للبطاقات
        'sidebar': '#2C3E50',         # أزرق داكن للشريط الجانبي

        # ألوان النص
        'text_primary': '#2C3E50',     # نص أساسي داكن
        'text_secondary': '#7F8C8D',   # نص ثانوي رمادي
        'text_light': '#FFFFFF',       # نص فاتح
        'text_muted': '#95A5A6',       # نص خافت

        # ألوان الحدود
        'border': '#BDC3C7',          # حدود رمادية
        'border_light': '#ECF0F1',    # حدود فاتحة
        'border_dark': '#95A5A6',     # حدود داكنة

        # ألوان التفاعل
        'hover': '#3498DB',           # لون التمرير
        'pressed': '#2980B9',         # لون الضغط
        'selected': '#3498DB',        # لون التحديد
        'disabled': '#BDC3C7',       # لون التعطيل
    }

    # أحجام الخطوط
    FONT_SIZES = {
        'title': 24,
        'heading': 18,
        'subheading': 16,
        'body': 14,
        'caption': 12,
        'small': 10
    }

    # المسافات والأبعاد
    SPACING = {
        'xs': 4,
        'sm': 8,
        'md': 16,
        'lg': 24,
        'xl': 32,
        'xxl': 48
    }

    @classmethod
    def get_main_window_style(cls) -> str:
        """الحصول على نمط النافذة الرئيسية"""
        return f"""
        QMainWindow {{
            background-color: {cls.COLORS['background']};
            color: {cls.COLORS['text_primary']};
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            font-size: {cls.FONT_SIZES['body']}px;
        }}

        /* شريط القوائم */
        QMenuBar {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_light']};
            border: none;
            padding: 4px;
        }}

        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 16px;
            border-radius: 4px;
            margin: 2px;
        }}

        QMenuBar::item:selected {{
            background-color: {cls.COLORS['hover']};
        }}

        QMenuBar::item:pressed {{
            background-color: {cls.COLORS['pressed']};
        }}

        /* القوائم المنسدلة */
        QMenu {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border']};
            border-radius: 8px;
            padding: 8px;
            color: {cls.COLORS['text_primary']};
        }}

        QMenu::item {{
            padding: 8px 24px;
            border-radius: 4px;
            margin: 2px;
        }}

        QMenu::item:selected {{
            background-color: {cls.COLORS['hover']};
            color: {cls.COLORS['text_light']};
        }}

        QMenu::separator {{
            height: 1px;
            background-color: {cls.COLORS['border_light']};
            margin: 4px 8px;
        }}

        /* شريط الأدوات */
        QToolBar {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border_light']};
            border-radius: 8px;
            padding: 8px;
            spacing: 4px;
        }}

        QToolButton {{
            background-color: transparent;
            border: none;
            border-radius: 6px;
            padding: 8px;
            margin: 2px;
            min-width: 32px;
            min-height: 32px;
        }}

        QToolButton:hover {{
            background-color: {cls.COLORS['hover']};
            color: {cls.COLORS['text_light']};
        }}

        QToolButton:pressed {{
            background-color: {cls.COLORS['pressed']};
        }}

        QToolButton:checked {{
            background-color: {cls.COLORS['selected']};
            color: {cls.COLORS['text_light']};
        }}

        /* شريط الحالة */
        QStatusBar {{
            background-color: {cls.COLORS['surface']};
            border-top: 1px solid {cls.COLORS['border_light']};
            color: {cls.COLORS['text_secondary']};
            padding: 4px 8px;
        }}
        """

    @classmethod
    def get_button_style(cls) -> str:
        """الحصول على نمط الأزرار"""
        return f"""
        /* الأزرار الأساسية */
        QPushButton {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_light']};
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: {cls.FONT_SIZES['body']}px;
            min-height: 20px;
        }}

        QPushButton:hover {{
            background-color: {cls.COLORS['hover']};
            transform: translateY(-1px);
        }}

        QPushButton:pressed {{
            background-color: {cls.COLORS['pressed']};
            transform: translateY(0px);
        }}

        QPushButton:disabled {{
            background-color: {cls.COLORS['disabled']};
            color: {cls.COLORS['text_muted']};
        }}

        /* أزرار ثانوية */
        QPushButton[class="secondary"] {{
            background-color: {cls.COLORS['surface']};
            color: {cls.COLORS['primary']};
            border: 2px solid {cls.COLORS['primary']};
        }}

        QPushButton[class="secondary"]:hover {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_light']};
        }}

        /* أزرار النجاح */
        QPushButton[class="success"] {{
            background-color: {cls.COLORS['success']};
        }}

        QPushButton[class="success"]:hover {{
            background-color: #229954;
        }}

        /* أزرار التحذير */
        QPushButton[class="warning"] {{
            background-color: {cls.COLORS['warning']};
        }}

        QPushButton[class="warning"]:hover {{
            background-color: #E67E22;
        }}

        /* أزرار الخطر */
        QPushButton[class="danger"] {{
            background-color: {cls.COLORS['accent']};
        }}

        QPushButton[class="danger"]:hover {{
            background-color: #C0392B;
        }}
        """

    @classmethod
    def get_input_style(cls) -> str:
        """الحصول على نمط حقول الإدخال"""
        return f"""
        /* حقول النص */
        QLineEdit {{
            background-color: {cls.COLORS['surface']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: {cls.FONT_SIZES['body']}px;
            color: {cls.COLORS['text_primary']};
        }}

        QLineEdit:focus {{
            border-color: {cls.COLORS['secondary']};
            outline: none;
        }}

        QLineEdit:disabled {{
            background-color: {cls.COLORS['card']};
            color: {cls.COLORS['text_muted']};
            border-color: {cls.COLORS['disabled']};
        }}

        /* مناطق النص */
        QTextEdit {{
            background-color: {cls.COLORS['surface']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 8px;
            padding: 12px;
            font-size: {cls.FONT_SIZES['body']}px;
            color: {cls.COLORS['text_primary']};
        }}

        QTextEdit:focus {{
            border-color: {cls.COLORS['secondary']};
        }}

        /* القوائم المنسدلة */
        QComboBox {{
            background-color: {cls.COLORS['surface']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: {cls.FONT_SIZES['body']}px;
            color: {cls.COLORS['text_primary']};
            min-width: 120px;
        }}

        QComboBox:focus {{
            border-color: {cls.COLORS['secondary']};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {cls.COLORS['text_secondary']};
            margin-right: 10px;
        }}

        QComboBox QAbstractItemView {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border']};
            border-radius: 8px;
            selection-background-color: {cls.COLORS['hover']};
            selection-color: {cls.COLORS['text_light']};
            padding: 4px;
        }}
        """

    @classmethod
    def get_table_style(cls) -> str:
        """الحصول على نمط الجداول"""
        return f"""
        /* الجداول */
        QTableWidget {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border_light']};
            border-radius: 8px;
            gridline-color: {cls.COLORS['border_light']};
            selection-background-color: {cls.COLORS['hover']};
            selection-color: {cls.COLORS['text_light']};
            font-size: {cls.FONT_SIZES['body']}px;
        }}

        QTableWidget::item {{
            padding: 12px 8px;
            border-bottom: 1px solid {cls.COLORS['border_light']};
        }}

        QTableWidget::item:selected {{
            background-color: {cls.COLORS['selected']};
            color: {cls.COLORS['text_light']};
        }}

        QHeaderView::section {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_light']};
            padding: 12px 8px;
            border: none;
            font-weight: 600;
        }}

        QHeaderView::section:horizontal {{
            border-right: 1px solid {cls.COLORS['primary_light']};
        }}

        QHeaderView::section:vertical {{
            border-bottom: 1px solid {cls.COLORS['primary_light']};
        }}
        """

    @classmethod
    def get_tab_style(cls) -> str:
        """الحصول على نمط التبويبات"""
        return f"""
        /* التبويبات */
        QTabWidget::pane {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border_light']};
            border-radius: 8px;
            margin-top: -1px;
        }}

        QTabBar::tab {{
            background-color: {cls.COLORS['card']};
            color: {cls.COLORS['text_secondary']};
            padding: 12px 24px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            font-weight: 500;
        }}

        QTabBar::tab:selected {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_light']};
        }}

        QTabBar::tab:hover:!selected {{
            background-color: {cls.COLORS['hover']};
            color: {cls.COLORS['text_light']};
        }}
        """

    @classmethod
    def get_complete_style(cls) -> str:
        """الحصول على النمط الكامل للتطبيق"""
        return (
            cls.get_main_window_style() +
            cls.get_button_style() +
            cls.get_input_style() +
            cls.get_table_style() +
            cls.get_tab_style()
        )

    @classmethod
    def setup_application_font(cls, app) -> None:
        """إعداد خط التطبيق الافتراضي"""
        font = QFont("Segoe UI", cls.FONT_SIZES['body'])
        font.setStyleHint(QFont.StyleHint.SansSerif)
        app.setFont(font)

    @classmethod
    def create_color_palette(cls) -> QPalette:
        """إنشاء لوحة ألوان مخصصة"""
        palette = QPalette()

        # ألوان النوافذ
        palette.setColor(QPalette.ColorRole.Window, QColor(cls.COLORS['background']))
        palette.setColor(QPalette.ColorRole.WindowText, QColor(cls.COLORS['text_primary']))

        # ألوان القواعد
        palette.setColor(QPalette.ColorRole.Base, QColor(cls.COLORS['surface']))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(cls.COLORS['card']))

        # ألوان النص
        palette.setColor(QPalette.ColorRole.Text, QColor(cls.COLORS['text_primary']))
        palette.setColor(QPalette.ColorRole.BrightText, QColor(cls.COLORS['text_light']))

        # ألوان الأزرار
        palette.setColor(QPalette.ColorRole.Button, QColor(cls.COLORS['primary']))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(cls.COLORS['text_light']))

        # ألوان التحديد
        palette.setColor(QPalette.ColorRole.Highlight, QColor(cls.COLORS['selected']))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor(cls.COLORS['text_light']))

        return palette