#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي لتطبيق تصميم الأثاث
Backup Manager for Furniture Design Application
"""

import os
import json
import shutil
import zipfile
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QTextEdit, QGroupBox, QTableWidget,
    QTableWidgetItem, QProgressBar, QMessageBox, QFileDialog,
    QCheckBox, QDateTimeEdit, QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, QDateTime, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


class BackupType(Enum):
    """أنواع النسخ الاحتياطي"""
    FULL = "full"
    INCREMENTAL = "incremental"
    DIFFERENTIAL = "differential"
    CUSTOM = "custom"


class BackupStatus(Enum):
    """حالات النسخ الاحتياطي"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BackupConfig:
    """إعدادات النسخ الاحتياطي"""
    id: str
    name: str
    backup_type: BackupType
    source_paths: List[str]
    destination_path: str
    schedule_enabled: bool
    schedule_frequency: str  # daily, weekly, monthly
    schedule_time: str
    compression_enabled: bool
    encryption_enabled: bool
    retention_days: int
    exclude_patterns: List[str]
    created_date: str = ""
    last_backup: Optional[str] = None
    
    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class BackupRecord:
    """سجل النسخ الاحتياطي"""
    id: str
    config_id: str
    backup_type: BackupType
    status: BackupStatus
    start_time: str
    end_time: Optional[str]
    file_path: str
    file_size: int
    files_count: int
    error_message: Optional[str] = None
    
    @property
    def duration(self) -> Optional[str]:
        """مدة النسخ الاحتياطي"""
        if self.start_time and self.end_time:
            start = datetime.fromisoformat(self.start_time)
            end = datetime.fromisoformat(self.end_time)
            duration = end - start
            return str(duration).split('.')[0]  # إزالة الميكروثواني
        return None


class BackupWorker(QThread):
    """عامل النسخ الاحتياطي"""
    
    progress_updated = pyqtSignal(int, str)
    backup_completed = pyqtSignal(object)  # BackupRecord
    backup_failed = pyqtSignal(str, str)  # config_id, error_message
    
    def __init__(self, config: BackupConfig):
        super().__init__()
        self.config = config
        self.is_cancelled = False
        
    def run(self):
        """تشغيل النسخ الاحتياطي"""
        try:
            # إنشاء سجل النسخ الاحتياطي
            backup_record = BackupRecord(
                id=f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                config_id=self.config.id,
                backup_type=self.config.backup_type,
                status=BackupStatus.RUNNING,
                start_time=datetime.now().isoformat(),
                end_time=None,
                file_path="",
                file_size=0,
                files_count=0
            )
            
            self.progress_updated.emit(10, "بدء النسخ الاحتياطي...")
            
            # إنشاء مجلد الوجهة
            os.makedirs(self.config.destination_path, exist_ok=True)
            
            # تحديد اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"{self.config.name}_{timestamp}.zip"
            backup_filepath = os.path.join(self.config.destination_path, backup_filename)
            
            self.progress_updated.emit(20, "إنشاء أرشيف النسخ الاحتياطي...")
            
            # إنشاء الأرشيف
            files_count = 0
            with zipfile.ZipFile(backup_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for source_path in self.config.source_paths:
                    if self.is_cancelled:
                        break
                        
                    if os.path.isfile(source_path):
                        # نسخ ملف واحد
                        zipf.write(source_path, os.path.basename(source_path))
                        files_count += 1
                        
                    elif os.path.isdir(source_path):
                        # نسخ مجلد
                        for root, dirs, files in os.walk(source_path):
                            if self.is_cancelled:
                                break
                                
                            for file in files:
                                if self.should_exclude_file(file):
                                    continue
                                    
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path, source_path)
                                zipf.write(file_path, arcname)
                                files_count += 1
                                
                                # تحديث التقدم
                                if files_count % 10 == 0:
                                    progress = min(20 + (files_count // 10), 80)
                                    self.progress_updated.emit(progress, f"تم نسخ {files_count} ملف...")
            
            if self.is_cancelled:
                # حذف الملف المؤقت
                if os.path.exists(backup_filepath):
                    os.remove(backup_filepath)
                return
                
            self.progress_updated.emit(90, "إنهاء النسخ الاحتياطي...")
            
            # تحديث سجل النسخ الاحتياطي
            backup_record.end_time = datetime.now().isoformat()
            backup_record.file_path = backup_filepath
            backup_record.file_size = os.path.getsize(backup_filepath)
            backup_record.files_count = files_count
            backup_record.status = BackupStatus.COMPLETED
            
            self.progress_updated.emit(100, f"تم إنشاء النسخة الاحتياطية بنجاح! ({files_count} ملف)")
            self.backup_completed.emit(backup_record)
            
        except Exception as e:
            error_msg = f"خطأ في النسخ الاحتياطي: {str(e)}"
            self.backup_failed.emit(self.config.id, error_msg)
            
    def should_exclude_file(self, filename: str) -> bool:
        """فحص ما إذا كان يجب استبعاد الملف"""
        for pattern in self.config.exclude_patterns:
            if pattern in filename or filename.endswith(pattern):
                return True
        return False
        
    def cancel(self):
        """إلغاء النسخ الاحتياطي"""
        self.is_cancelled = True


class BackupManager(QWidget):
    """مدير النسخ الاحتياطي"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.configs: Dict[str, BackupConfig] = {}
        self.records: List[BackupRecord] = []
        self.current_worker: Optional[BackupWorker] = None
        self.scheduler_timer = QTimer()
        self.scheduler_timer.timeout.connect(self.check_scheduled_backups)
        self.scheduler_timer.start(60000)  # فحص كل دقيقة
        
        self.init_ui()
        self.load_configs()
        self.load_records()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # العنوان
        title_label = QLabel("نظام النسخ الاحتياطي")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 16px;
                background-color: {ModernStyleManager.COLORS['surface']};
                border-radius: 8px;
                margin-bottom: 8px;
            }}
        """)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        new_config_btn = QPushButton("إعداد جديد")
        new_config_btn.setIcon(IconsManager.get_standard_icon('add'))
        new_config_btn.clicked.connect(self.create_new_config)
        
        edit_config_btn = QPushButton("تعديل")
        edit_config_btn.setIcon(IconsManager.get_standard_icon('edit'))
        edit_config_btn.clicked.connect(self.edit_config)
        
        delete_config_btn = QPushButton("حذف")
        delete_config_btn.setIcon(IconsManager.get_standard_icon('delete'))
        delete_config_btn.clicked.connect(self.delete_config)
        
        run_backup_btn = QPushButton("تشغيل النسخ الاحتياطي")
        run_backup_btn.setIcon(IconsManager.get_standard_icon('success'))
        run_backup_btn.clicked.connect(self.run_backup)
        
        restore_btn = QPushButton("استعادة")
        restore_btn.setIcon(IconsManager.get_standard_icon('refresh'))
        restore_btn.clicked.connect(self.restore_backup)
        
        toolbar_layout.addWidget(new_config_btn)
        toolbar_layout.addWidget(edit_config_btn)
        toolbar_layout.addWidget(delete_config_btn)
        toolbar_layout.addSeparator()
        toolbar_layout.addWidget(run_backup_btn)
        toolbar_layout.addWidget(restore_btn)
        toolbar_layout.addStretch()
        
        # المحتوى الرئيسي
        main_layout = QHBoxLayout()
        
        # لوحة الإعدادات
        self.create_configs_panel(main_layout)
        
        # لوحة السجلات
        self.create_records_panel(main_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # تسمية الحالة
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                padding: 8px;
                background-color: {ModernStyleManager.COLORS['card']};
                border-radius: 4px;
                font-weight: bold;
            }}
        """)
        
        # إضافة العناصر للتخطيط الرئيسي
        layout.addWidget(title_label)
        layout.addLayout(toolbar_layout)
        layout.addLayout(main_layout)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        
    def create_configs_panel(self, parent_layout):
        """إنشاء لوحة الإعدادات"""
        configs_group = QGroupBox("إعدادات النسخ الاحتياطي")
        configs_layout = QVBoxLayout(configs_group)
        
        self.configs_list = QListWidget()
        self.configs_list.itemClicked.connect(self.on_config_selected)
        
        configs_layout.addWidget(self.configs_list)
        
        parent_layout.addWidget(configs_group)
        
    def create_records_panel(self, parent_layout):
        """إنشاء لوحة السجلات"""
        records_group = QGroupBox("سجل النسخ الاحتياطي")
        records_layout = QVBoxLayout(records_group)
        
        self.records_table = QTableWidget()
        self.records_table.setColumnCount(7)
        self.records_table.setHorizontalHeaderLabels([
            "التاريخ", "الاسم", "النوع", "الحالة", "الحجم", "عدد الملفات", "المدة"
        ])
        
        records_layout.addWidget(self.records_table)
        
        parent_layout.addWidget(records_group)
        
    def load_configs(self):
        """تحميل الإعدادات"""
        try:
            configs_file = "backup_configs.json"
            if os.path.exists(configs_file):
                with open(configs_file, 'r', encoding='utf-8') as f:
                    configs_data = json.load(f)
                    
                for config_data in configs_data:
                    config = BackupConfig(**config_data)
                    self.configs[config.id] = config
                    
                self.refresh_configs_list()
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل الإعدادات: {str(e)}")
            
    def save_configs(self):
        """حفظ الإعدادات"""
        try:
            configs_data = [asdict(config) for config in self.configs.values()]
            
            with open("backup_configs.json", 'w', encoding='utf-8') as f:
                json.dump(configs_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في حفظ الإعدادات: {str(e)}")
            
    def load_records(self):
        """تحميل السجلات"""
        try:
            records_file = "backup_records.json"
            if os.path.exists(records_file):
                with open(records_file, 'r', encoding='utf-8') as f:
                    records_data = json.load(f)
                    
                self.records = [BackupRecord(**record_data) for record_data in records_data]
                self.refresh_records_table()
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل السجلات: {str(e)}")
            
    def save_records(self):
        """حفظ السجلات"""
        try:
            records_data = [asdict(record) for record in self.records]
            
            with open("backup_records.json", 'w', encoding='utf-8') as f:
                json.dump(records_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في حفظ السجلات: {str(e)}")
            
    def refresh_configs_list(self):
        """تحديث قائمة الإعدادات"""
        self.configs_list.clear()
        
        for config in self.configs.values():
            item = QListWidgetItem()
            item.setText(f"{config.name} ({config.backup_type.value})")
            item.setData(Qt.ItemDataRole.UserRole, config.id)
            
            # تعيين أيقونة حسب نوع النسخ الاحتياطي
            if config.backup_type == BackupType.FULL:
                item.setIcon(IconsManager.get_standard_icon('database'))
            elif config.backup_type == BackupType.INCREMENTAL:
                item.setIcon(IconsManager.get_standard_icon('refresh'))
            else:
                item.setIcon(IconsManager.get_standard_icon('save'))
                
            self.configs_list.addItem(item)
            
    def refresh_records_table(self):
        """تحديث جدول السجلات"""
        self.records_table.setRowCount(len(self.records))
        
        for row, record in enumerate(sorted(self.records, key=lambda x: x.start_time, reverse=True)):
            # التاريخ
            start_time = datetime.fromisoformat(record.start_time)
            self.records_table.setItem(row, 0, QTableWidgetItem(start_time.strftime('%Y-%m-%d %H:%M')))
            
            # الاسم
            config = self.configs.get(record.config_id)
            config_name = config.name if config else "غير معروف"
            self.records_table.setItem(row, 1, QTableWidgetItem(config_name))
            
            # النوع
            self.records_table.setItem(row, 2, QTableWidgetItem(record.backup_type.value))
            
            # الحالة مع لون
            status_item = QTableWidgetItem(record.status.value)
            if record.status == BackupStatus.COMPLETED:
                status_item.setBackground(QColor(ModernStyleManager.COLORS['success']))
            elif record.status == BackupStatus.FAILED:
                status_item.setBackground(QColor(ModernStyleManager.COLORS['accent']))
            elif record.status == BackupStatus.RUNNING:
                status_item.setBackground(QColor(ModernStyleManager.COLORS['warning']))
            self.records_table.setItem(row, 3, status_item)
            
            # الحجم
            size_mb = record.file_size / (1024 * 1024) if record.file_size > 0 else 0
            self.records_table.setItem(row, 4, QTableWidgetItem(f"{size_mb:.1f} MB"))
            
            # عدد الملفات
            self.records_table.setItem(row, 5, QTableWidgetItem(str(record.files_count)))
            
            # المدة
            duration = record.duration or "غير محدد"
            self.records_table.setItem(row, 6, QTableWidgetItem(duration))
            
    def on_config_selected(self, item: QListWidgetItem):
        """عند تحديد إعداد"""
        config_id = item.data(Qt.ItemDataRole.UserRole)
        config = self.configs.get(config_id)
        
        if config:
            # عرض تفاصيل الإعداد
            details = f"""
            الاسم: {config.name}
            النوع: {config.backup_type.value}
            المسارات المصدر: {len(config.source_paths)}
            مجلد الوجهة: {config.destination_path}
            الجدولة: {'مفعلة' if config.schedule_enabled else 'معطلة'}
            آخر نسخة احتياطية: {config.last_backup or 'لا توجد'}
            """
            
            self.status_label.setText(details.strip())
            
    def create_new_config(self):
        """إنشاء إعداد جديد"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إنشاء إعداد جديد قريباً")
        
    def edit_config(self):
        """تعديل إعداد"""
        current_item = self.configs_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار إعداد للتعديل")
            return
            
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ تعديل الإعداد قريباً")
        
    def delete_config(self):
        """حذف إعداد"""
        current_item = self.configs_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار إعداد للحذف")
            return
            
        config_id = current_item.data(Qt.ItemDataRole.UserRole)
        config = self.configs.get(config_id)
        
        if config:
            reply = QMessageBox.question(self, "تأكيد الحذف",
                                       f"هل أنت متأكد من حذف إعداد '{config.name}'؟",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                del self.configs[config_id]
                self.save_configs()
                self.refresh_configs_list()
                QMessageBox.information(self, "نجح الحذف", "تم حذف الإعداد بنجاح")
                
    def run_backup(self):
        """تشغيل النسخ الاحتياطي"""
        current_item = self.configs_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار إعداد لتشغيل النسخ الاحتياطي")
            return
            
        if self.current_worker and self.current_worker.isRunning():
            QMessageBox.warning(self, "تحذير", "يوجد نسخ احتياطي قيد التشغيل بالفعل")
            return
            
        config_id = current_item.data(Qt.ItemDataRole.UserRole)
        config = self.configs.get(config_id)
        
        if config:
            self.start_backup(config)
            
    def start_backup(self, config: BackupConfig):
        """بدء النسخ الاحتياطي"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        self.current_worker = BackupWorker(config)
        self.current_worker.progress_updated.connect(self.update_progress)
        self.current_worker.backup_completed.connect(self.on_backup_completed)
        self.current_worker.backup_failed.connect(self.on_backup_failed)
        self.current_worker.start()
        
    def update_progress(self, value: int, message: str):
        """تحديث التقدم"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        
    def on_backup_completed(self, record: BackupRecord):
        """عند اكتمال النسخ الاحتياطي"""
        self.progress_bar.setVisible(False)
        self.records.append(record)
        self.save_records()
        self.refresh_records_table()
        
        # تحديث آخر نسخة احتياطية في الإعداد
        config = self.configs.get(record.config_id)
        if config:
            config.last_backup = record.start_time
            self.save_configs()
            
        QMessageBox.information(self, "نجح النسخ الاحتياطي", 
                              f"تم إنشاء النسخة الاحتياطية بنجاح!\n\n"
                              f"الملف: {record.file_path}\n"
                              f"الحجم: {record.file_size / (1024*1024):.1f} MB\n"
                              f"عدد الملفات: {record.files_count}")
        
    def on_backup_failed(self, config_id: str, error_message: str):
        """عند فشل النسخ الاحتياطي"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("فشل النسخ الاحتياطي")
        
        QMessageBox.critical(self, "خطأ في النسخ الاحتياطي", error_message)
        
    def restore_backup(self):
        """استعادة النسخة الاحتياطية"""
        current_row = self.records_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار نسخة احتياطية للاستعادة")
            return
            
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ استعادة النسخة الاحتياطية قريباً")
        
    def check_scheduled_backups(self):
        """فحص النسخ الاحتياطي المجدولة"""
        current_time = datetime.now()
        
        for config in self.configs.values():
            if not config.schedule_enabled:
                continue
                
            # فحص ما إذا كان الوقت مناسب للنسخ الاحتياطي
            # (تنفيذ مبسط - يمكن تطويره أكثر)
            if self.should_run_scheduled_backup(config, current_time):
                self.start_backup(config)
                break  # تشغيل نسخة واحدة في المرة
                
    def should_run_scheduled_backup(self, config: BackupConfig, current_time: datetime) -> bool:
        """فحص ما إذا كان يجب تشغيل النسخ الاحتياطي المجدول"""
        # تنفيذ مبسط - يمكن تطويره لدعم جدولة أكثر تعقيداً
        if not config.last_backup:
            return True
            
        last_backup_time = datetime.fromisoformat(config.last_backup)
        
        if config.schedule_frequency == "daily":
            return (current_time - last_backup_time).days >= 1
        elif config.schedule_frequency == "weekly":
            return (current_time - last_backup_time).days >= 7
        elif config.schedule_frequency == "monthly":
            return (current_time - last_backup_time).days >= 30
            
        return False
