# CutList Pro - Furniture Designer Edition Requirements
# متطلبات تطبيق مصمم الأثاث الاحترافي المتقدم - PyQt6

# Core GUI Framework - إطار العمل الأساسي المحدث
PyQt6>=6.4.0
PyQt6-tools>=6.4.0

# 3D Model Processing and Visualization - معالجة النماذج ثلاثية الأبعاد
trimesh>=4.0.0
numpy>=1.20.0
scipy>=1.7.0

# Data Processing and Analysis - معالجة وتحليل البيانات
pandas>=1.3.0

# Excel Export with Advanced Features - تصدير Excel متقدم
openpyxl>=3.0.0
xlsxwriter>=3.0.0

# PDF Export with Arabic Support - تصدير PDF مع دعم العربية
reportlab>=3.6.0
arabic-reshaper>=3.0.0
python-bidi>=0.4.0

# Image Processing and Visualization - معالجة الصور والتصور
Pillow>=8.0.0
matplotlib>=3.5.0

# System Information (for launcher) - معلومات النظام
psutil>=5.8.0

# Date and Time Processing - معالجة التاريخ والوقت
python-dateutil>=2.8.0

# QR Code and Barcode Support - دعم رموز QR والباركود
qrcode>=7.3.0

# Optional Professional Features - ميزات احترافية اختيارية
# Open3D>=0.15.0          # تصور ثلاثي الأبعاد متقدم
# vtk>=9.1.0              # مكتبة التصور العلمي
# scikit-learn>=1.0.0     # تعلم الآلة للتحسين
# SQLAlchemy>=1.4.0       # دعم قواعد البيانات المتقدمة

# Development Tools - أدوات التطوير (اختيارية)
# pytest>=6.2.0          # اختبار الوحدة
# black>=21.0.0           # تنسيق الكود
# flake8>=4.0.0           # فحص جودة الكود
