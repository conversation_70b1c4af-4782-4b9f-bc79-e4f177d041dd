#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تكامل آلات CNC لتطبيق تصميم الأثاث
CNC Machine Integration System for Furniture Design Application
"""

import os
import json
import time
import socket
from datetime import datetime

# محاولة استيراد مكتبة serial
try:
    import serial
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False
    print("⚠️  مكتبة pyserial غير متوفرة - سيتم تعطيل دعم الاتصال التسلسلي")
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit, QGroupBox,
    QTableWidget, QTableWidgetItem, Q<PERSON><PERSON>ress<PERSON><PERSON>, Q<PERSON>essageBox,
    QTab<PERSON>idget, QCheckBox, QLineEdit, QFileDialog
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


class CNCMachineType(Enum):
    """أنواع آلات CNC"""
    ROUTER = "router"
    MILL = "mill"
    LATHE = "lathe"
    PLASMA = "plasma"
    LASER = "laser"
    WATERJET = "waterjet"


class CNCConnectionType(Enum):
    """أنواع الاتصال مع آلات CNC"""
    SERIAL = "serial"
    ETHERNET = "ethernet"
    USB = "usb"
    WIRELESS = "wireless"


class CNCJobStatus(Enum):
    """حالات مهام CNC"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class CNCMachine:
    """معلومات آلة CNC"""
    id: str
    name: str
    machine_type: CNCMachineType
    connection_type: CNCConnectionType
    connection_params: Dict[str, Any]
    work_area: Tuple[float, float, float]  # X, Y, Z في مم
    max_feed_rate: float  # مم/دقيقة
    spindle_speed_range: Tuple[int, int]  # دورة/دقيقة
    supported_tools: List[str]
    is_connected: bool = False
    last_status: str = "غير متصل"
    created_date: str = ""

    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class CNCJob:
    """مهمة CNC"""
    id: str
    name: str
    machine_id: str
    gcode_file: str
    material_type: str
    material_thickness: float
    estimated_time: int  # بالدقائق
    tool_list: List[str]
    status: CNCJobStatus
    progress: float = 0.0
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    created_date: str = ""
    notes: str = ""

    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


class CNCCommunicator(QThread):
    """مُتصل آلات CNC"""

    status_updated = pyqtSignal(str, str)  # machine_id, status
    job_progress = pyqtSignal(str, float)  # job_id, progress
    error_occurred = pyqtSignal(str, str)  # machine_id, error_message

    def __init__(self, machine: CNCMachine):
        super().__init__()
        self.machine = machine
        self.connection = None
        self.is_running = False

    def run(self):
        """تشغيل الاتصال"""
        self.is_running = True

        try:
            self.connect_to_machine()

            while self.is_running:
                if self.connection:
                    self.check_machine_status()
                self.msleep(1000)  # فحص كل ثانية

        except Exception as e:
            self.error_occurred.emit(self.machine.id, str(e))
        finally:
            self.disconnect_from_machine()

    def connect_to_machine(self):
        """الاتصال بالآلة"""
        try:
            if self.machine.connection_type == CNCConnectionType.SERIAL:
                self.connect_serial()
            elif self.machine.connection_type == CNCConnectionType.ETHERNET:
                self.connect_ethernet()
            elif self.machine.connection_type == CNCConnectionType.USB:
                self.connect_usb()
            else:
                raise Exception(f"نوع اتصال غير مدعوم: {self.machine.connection_type}")

            self.status_updated.emit(self.machine.id, "متصل")

        except Exception as e:
            self.error_occurred.emit(self.machine.id, f"فشل الاتصال: {str(e)}")

    def connect_serial(self):
        """الاتصال عبر المنفذ التسلسلي"""
        if not SERIAL_AVAILABLE:
            raise Exception("مكتبة pyserial غير متوفرة. يرجى تثبيتها: pip install pyserial")

        params = self.machine.connection_params
        port = params.get('port', 'COM1')
        baudrate = params.get('baudrate', 9600)

        self.connection = serial.Serial(
            port=port,
            baudrate=baudrate,
            timeout=1
        )

    def connect_ethernet(self):
        """الاتصال عبر الشبكة"""
        params = self.machine.connection_params
        host = params.get('host', '*************')
        port = params.get('port', 8080)

        self.connection = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.connection.connect((host, port))

    def connect_usb(self):
        """الاتصال عبر USB"""
        # تنفيذ اتصال USB (يحتاج مكتبات خاصة)
        raise Exception("اتصال USB غير مُنفذ بعد")

    def disconnect_from_machine(self):
        """قطع الاتصال"""
        if self.connection:
            try:
                self.connection.close()
            except:
                pass
            self.connection = None

        self.status_updated.emit(self.machine.id, "غير متصل")

    def check_machine_status(self):
        """فحص حالة الآلة"""
        try:
            if self.machine.connection_type == CNCConnectionType.SERIAL:
                # إرسال أمر فحص الحالة
                self.connection.write(b'?\n')
                response = self.connection.readline().decode().strip()
                self.parse_status_response(response)

            elif self.machine.connection_type == CNCConnectionType.ETHERNET:
                # إرسال طلب HTTP لفحص الحالة
                request = b'GET /status HTTP/1.1\r\nHost: machine\r\n\r\n'
                self.connection.send(request)
                response = self.connection.recv(1024).decode()
                self.parse_status_response(response)

        except Exception as e:
            self.error_occurred.emit(self.machine.id, f"خطأ في فحص الحالة: {str(e)}")

    def parse_status_response(self, response: str):
        """تحليل رد الحالة"""
        # تحليل بسيط - يمكن تطويره حسب بروتوكول الآلة
        if "idle" in response.lower():
            status = "خامل"
        elif "run" in response.lower():
            status = "يعمل"
        elif "alarm" in response.lower():
            status = "إنذار"
        else:
            status = "غير معروف"

        self.status_updated.emit(self.machine.id, status)

    def send_gcode(self, gcode: str):
        """إرسال G-code للآلة"""
        try:
            if self.connection:
                if self.machine.connection_type == CNCConnectionType.SERIAL:
                    self.connection.write(gcode.encode() + b'\n')
                elif self.machine.connection_type == CNCConnectionType.ETHERNET:
                    request = f'POST /gcode HTTP/1.1\r\nContent-Length: {len(gcode)}\r\n\r\n{gcode}'
                    self.connection.send(request.encode())

        except Exception as e:
            self.error_occurred.emit(self.machine.id, f"خطأ في إرسال G-code: {str(e)}")

    def stop(self):
        """إيقاف الخيط"""
        self.is_running = False


class CNCMachineManager(QWidget):
    """مدير آلات CNC"""

    machine_connected = pyqtSignal(str)  # machine_id
    machine_disconnected = pyqtSignal(str)  # machine_id
    job_completed = pyqtSignal(str)  # job_id

    def __init__(self, parent=None):
        super().__init__(parent)
        self.machines: Dict[str, CNCMachine] = {}
        self.jobs: Dict[str, CNCJob] = {}
        self.communicators: Dict[str, CNCCommunicator] = {}
        self.init_ui()
        self.load_machines()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # العنوان
        title_label = QLabel("إدارة آلات CNC")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 16px;
                background-color: {ModernStyleManager.COLORS['surface']};
                border-radius: 8px;
                margin-bottom: 8px;
            }}
        """)

        # التبويبات
        self.tabs = QTabWidget()

        # تبويب الآلات
        self.create_machines_tab()

        # تبويب المهام
        self.create_jobs_tab()

        # تبويب المراقبة
        self.create_monitoring_tab()

        layout.addWidget(title_label)
        layout.addWidget(self.tabs)

    def create_machines_tab(self):
        """إنشاء تبويب الآلات"""
        machines_widget = QWidget()
        layout = QVBoxLayout(machines_widget)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        add_machine_btn = QPushButton("إضافة آلة")
        add_machine_btn.setIcon(IconsManager.get_standard_icon('add'))
        add_machine_btn.clicked.connect(self.add_machine)

        edit_machine_btn = QPushButton("تعديل")
        edit_machine_btn.setIcon(IconsManager.get_standard_icon('edit'))
        edit_machine_btn.clicked.connect(self.edit_machine)

        delete_machine_btn = QPushButton("حذف")
        delete_machine_btn.setIcon(IconsManager.get_standard_icon('delete'))
        delete_machine_btn.clicked.connect(self.delete_machine)

        connect_btn = QPushButton("اتصال")
        connect_btn.setIcon(IconsManager.get_standard_icon('success'))
        connect_btn.clicked.connect(self.connect_machine)

        disconnect_btn = QPushButton("قطع الاتصال")
        disconnect_btn.setIcon(IconsManager.get_standard_icon('close'))
        disconnect_btn.clicked.connect(self.disconnect_machine)

        toolbar_layout.addWidget(add_machine_btn)
        toolbar_layout.addWidget(edit_machine_btn)
        toolbar_layout.addWidget(delete_machine_btn)
        toolbar_layout.addSeparator()
        toolbar_layout.addWidget(connect_btn)
        toolbar_layout.addWidget(disconnect_btn)
        toolbar_layout.addStretch()

        # جدول الآلات
        self.machines_table = QTableWidget()
        self.machines_table.setColumnCount(6)
        self.machines_table.setHorizontalHeaderLabels([
            "الاسم", "النوع", "الاتصال", "الحالة", "منطقة العمل", "تاريخ الإضافة"
        ])

        layout.addLayout(toolbar_layout)
        layout.addWidget(self.machines_table)

        self.tabs.addTab(machines_widget, "الآلات")

    def create_jobs_tab(self):
        """إنشاء تبويب المهام"""
        jobs_widget = QWidget()
        layout = QVBoxLayout(jobs_widget)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        new_job_btn = QPushButton("مهمة جديدة")
        new_job_btn.setIcon(IconsManager.get_standard_icon('add'))
        new_job_btn.clicked.connect(self.create_new_job)

        start_job_btn = QPushButton("بدء المهمة")
        start_job_btn.setIcon(IconsManager.get_standard_icon('success'))
        start_job_btn.clicked.connect(self.start_job)

        pause_job_btn = QPushButton("إيقاف مؤقت")
        pause_job_btn.setIcon(IconsManager.get_standard_icon('warning'))
        pause_job_btn.clicked.connect(self.pause_job)

        stop_job_btn = QPushButton("إيقاف")
        stop_job_btn.setIcon(IconsManager.get_standard_icon('close'))
        stop_job_btn.clicked.connect(self.stop_job)

        toolbar_layout.addWidget(new_job_btn)
        toolbar_layout.addWidget(start_job_btn)
        toolbar_layout.addWidget(pause_job_btn)
        toolbar_layout.addWidget(stop_job_btn)
        toolbar_layout.addStretch()

        # جدول المهام
        self.jobs_table = QTableWidget()
        self.jobs_table.setColumnCount(7)
        self.jobs_table.setHorizontalHeaderLabels([
            "اسم المهمة", "الآلة", "الحالة", "التقدم", "الوقت المقدر", "تاريخ الإنشاء", "ملاحظات"
        ])

        layout.addLayout(toolbar_layout)
        layout.addWidget(self.jobs_table)

        self.tabs.addTab(jobs_widget, "المهام")

    def create_monitoring_tab(self):
        """إنشاء تبويب المراقبة"""
        monitoring_widget = QWidget()
        layout = QVBoxLayout(monitoring_widget)

        # معلومات الحالة الحية
        status_group = QGroupBox("الحالة الحية")
        status_layout = QVBoxLayout(status_group)

        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumHeight(200)
        self.status_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {ModernStyleManager.COLORS['card']};
                border: 1px solid {ModernStyleManager.COLORS['border']};
                border-radius: 4px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }}
        """)

        status_layout.addWidget(self.status_text)

        # أدوات التحكم السريع
        control_group = QGroupBox("التحكم السريع")
        control_layout = QHBoxLayout(control_group)

        emergency_stop_btn = QPushButton("إيقاف طوارئ")
        emergency_stop_btn.setIcon(IconsManager.get_standard_icon('error'))
        emergency_stop_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ModernStyleManager.COLORS['accent']};
                color: {ModernStyleManager.COLORS['text_light']};
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 8px;
            }}
            QPushButton:hover {{
                background-color: #C0392B;
            }}
        """)
        emergency_stop_btn.clicked.connect(self.emergency_stop)

        home_all_btn = QPushButton("العودة للنقطة الأصل")
        home_all_btn.setIcon(IconsManager.get_standard_icon('home'))
        home_all_btn.clicked.connect(self.home_all_machines)

        control_layout.addWidget(emergency_stop_btn)
        control_layout.addWidget(home_all_btn)
        control_layout.addStretch()

        layout.addWidget(status_group)
        layout.addWidget(control_group)
        layout.addStretch()

        self.tabs.addTab(monitoring_widget, "المراقبة")

    def load_machines(self):
        """تحميل الآلات المحفوظة"""
        try:
            machines_file = "cnc_machines.json"
            if os.path.exists(machines_file):
                with open(machines_file, 'r', encoding='utf-8') as f:
                    machines_data = json.load(f)

                for machine_data in machines_data:
                    machine = CNCMachine(**machine_data)
                    self.machines[machine.id] = machine

                self.refresh_machines_table()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل الآلات: {str(e)}")

    def save_machines(self):
        """حفظ الآلات"""
        try:
            machines_data = [asdict(machine) for machine in self.machines.values()]

            with open("cnc_machines.json", 'w', encoding='utf-8') as f:
                json.dump(machines_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في حفظ الآلات: {str(e)}")

    def refresh_machines_table(self):
        """تحديث جدول الآلات"""
        self.machines_table.setRowCount(len(self.machines))

        for row, machine in enumerate(self.machines.values()):
            self.machines_table.setItem(row, 0, QTableWidgetItem(machine.name))
            self.machines_table.setItem(row, 1, QTableWidgetItem(machine.machine_type.value))
            self.machines_table.setItem(row, 2, QTableWidgetItem(machine.connection_type.value))

            # حالة الاتصال مع لون
            status_item = QTableWidgetItem(machine.last_status)
            if machine.is_connected:
                status_item.setBackground(QColor(ModernStyleManager.COLORS['success']))
            else:
                status_item.setBackground(QColor(ModernStyleManager.COLORS['accent']))
            self.machines_table.setItem(row, 3, status_item)

            work_area = f"{machine.work_area[0]}×{machine.work_area[1]}×{machine.work_area[2]}"
            self.machines_table.setItem(row, 4, QTableWidgetItem(work_area))
            self.machines_table.setItem(row, 5, QTableWidgetItem(machine.created_date[:10]))

    def add_machine(self):
        """إضافة آلة جديدة"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إضافة آلة جديدة قريباً")

    def edit_machine(self):
        """تعديل آلة"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ تعديل الآلة قريباً")

    def delete_machine(self):
        """حذف آلة"""
        current_row = self.machines_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار آلة للحذف")
            return

        QMessageBox.information(self, "معلومات", "سيتم تنفيذ حذف الآلة قريباً")

    def connect_machine(self):
        """الاتصال بآلة"""
        current_row = self.machines_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار آلة للاتصال")
            return

        QMessageBox.information(self, "معلومات", "سيتم تنفيذ الاتصال بالآلة قريباً")

    def disconnect_machine(self):
        """قطع الاتصال مع آلة"""
        current_row = self.machines_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار آلة لقطع الاتصال")
            return

        QMessageBox.information(self, "معلومات", "سيتم تنفيذ قطع الاتصال قريباً")

    def create_new_job(self):
        """إنشاء مهمة جديدة"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إنشاء مهمة جديدة قريباً")

    def start_job(self):
        """بدء مهمة"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ بدء المهمة قريباً")

    def pause_job(self):
        """إيقاف مهمة مؤقتاً"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إيقاف المهمة مؤقتاً قريباً")

    def stop_job(self):
        """إيقاف مهمة"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إيقاف المهمة قريباً")

    def emergency_stop(self):
        """إيقاف طوارئ"""
        reply = QMessageBox.question(self, "إيقاف طوارئ",
                                   "هل أنت متأكد من إيقاف جميع الآلات فوراً؟",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            QMessageBox.information(self, "معلومات", "تم تنفيذ إيقاف الطوارئ")

    def home_all_machines(self):
        """إرجاع جميع الآلات للنقطة الأصل"""
        QMessageBox.information(self, "معلومات", "سيتم إرجاع جميع الآلات للنقطة الأصل قريباً")
