#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام السحب والإفلات للملفات في تطبيق تصميم الأثاث
Drag and Drop File Handler for Furniture Design Application
"""

import os
import mimetypes
from typing import List, Dict, Callable, Optional
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QMessageBox, QProgressBar,
    QGroupBox, QTextEdit, QFileDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QMimeData, QUrl, QThread
from PyQt6.QtGui import QDragEnterEvent, QDropEvent, QPixmap, QPainter, QColor, QFont

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


class FileProcessor(QThread):
    """معالج الملفات في خيط منفصل"""
    
    file_processed = pyqtSignal(str, dict)  # file_path, file_info
    processing_progress = pyqtSignal(int, str)  # progress, message
    processing_error = pyqtSignal(str, str)  # file_path, error_message
    
    def __init__(self, file_paths: List[str], file_handlers: Dict[str, Callable]):
        super().__init__()
        self.file_paths = file_paths
        self.file_handlers = file_handlers
        
    def run(self):
        """معالجة الملفات"""
        total_files = len(self.file_paths)
        
        for i, file_path in enumerate(self.file_paths):
            try:
                # تحديث التقدم
                progress = int((i / total_files) * 100)
                self.processing_progress.emit(progress, f"معالجة: {os.path.basename(file_path)}")
                
                # تحديد نوع الملف
                file_ext = os.path.splitext(file_path)[1].lower()
                
                # معلومات الملف الأساسية
                file_info = {
                    'path': file_path,
                    'name': os.path.basename(file_path),
                    'size': os.path.getsize(file_path),
                    'extension': file_ext,
                    'mime_type': mimetypes.guess_type(file_path)[0],
                    'is_supported': file_ext in self.file_handlers
                }
                
                # معالجة الملف إذا كان مدعوماً
                if file_ext in self.file_handlers:
                    handler = self.file_handlers[file_ext]
                    additional_info = handler(file_path)
                    file_info.update(additional_info)
                
                self.file_processed.emit(file_path, file_info)
                
            except Exception as e:
                self.processing_error.emit(file_path, str(e))
        
        # إنهاء المعالجة
        self.processing_progress.emit(100, "تمت المعالجة بنجاح")


class DropZoneWidget(QWidget):
    """منطقة السحب والإفلات"""
    
    files_dropped = pyqtSignal(list)  # List[str] - file paths
    
    def __init__(self, accepted_extensions: List[str] = None, parent=None):
        super().__init__(parent)
        self.accepted_extensions = accepted_extensions or []
        self.is_dragging = False
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setAcceptDrops(True)
        self.setMinimumHeight(200)
        
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # أيقونة السحب والإفلات
        icon_label = QLabel("📁")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 48px;
                color: {ModernStyleManager.COLORS['text_muted']};
                margin-bottom: 16px;
            }}
        """)
        
        # النص الرئيسي
        self.main_text = QLabel("اسحب الملفات هنا")
        self.main_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.main_text.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['text_secondary']};
                margin-bottom: 8px;
            }}
        """)
        
        # النص الفرعي
        extensions_text = ", ".join(self.accepted_extensions) if self.accepted_extensions else "جميع الملفات"
        self.sub_text = QLabel(f"الصيغ المدعومة: {extensions_text}")
        self.sub_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.sub_text.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['body']}px;
                color: {ModernStyleManager.COLORS['text_muted']};
                margin-bottom: 16px;
            }}
        """)
        
        # زر التصفح
        browse_btn = QPushButton("أو انقر للتصفح")
        browse_btn.setIcon(IconsManager.get_standard_icon('open'))
        browse_btn.clicked.connect(self.browse_files)
        browse_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: 2px dashed {ModernStyleManager.COLORS['border']};
                border-radius: 8px;
                padding: 12px 24px;
                color: {ModernStyleManager.COLORS['text_secondary']};
                font-weight: 500;
            }}
            QPushButton:hover {{
                border-color: {ModernStyleManager.COLORS['secondary']};
                color: {ModernStyleManager.COLORS['secondary']};
            }}
        """)
        
        layout.addWidget(icon_label)
        layout.addWidget(self.main_text)
        layout.addWidget(self.sub_text)
        layout.addWidget(browse_btn)
        
        self.update_style()
        
    def update_style(self):
        """تحديث نمط المنطقة"""
        if self.is_dragging:
            border_color = ModernStyleManager.COLORS['secondary']
            bg_color = ModernStyleManager.COLORS['card']
            text_color = ModernStyleManager.COLORS['secondary']
        else:
            border_color = ModernStyleManager.COLORS['border']
            bg_color = ModernStyleManager.COLORS['surface']
            text_color = ModernStyleManager.COLORS['text_muted']
            
        self.setStyleSheet(f"""
            DropZoneWidget {{
                background-color: {bg_color};
                border: 3px dashed {border_color};
                border-radius: 12px;
            }}
        """)
        
        self.main_text.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {text_color};
                margin-bottom: 8px;
            }}
        """)
        
    def dragEnterEvent(self, event: QDragEnterEvent):
        """عند دخول السحب"""
        if event.mimeData().hasUrls():
            # فحص الملفات
            valid_files = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    if self.is_file_accepted(file_path):
                        valid_files.append(file_path)
            
            if valid_files:
                self.is_dragging = True
                self.update_style()
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
            
    def dragLeaveEvent(self, event):
        """عند مغادرة السحب"""
        self.is_dragging = False
        self.update_style()
        
    def dropEvent(self, event: QDropEvent):
        """عند الإفلات"""
        self.is_dragging = False
        self.update_style()
        
        if event.mimeData().hasUrls():
            file_paths = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    if self.is_file_accepted(file_path):
                        file_paths.append(file_path)
            
            if file_paths:
                self.files_dropped.emit(file_paths)
                event.acceptProposedAction()
            else:
                QMessageBox.warning(self, "ملفات غير مدعومة", 
                                  "الملفات المحددة غير مدعومة في هذا التطبيق")
                
    def is_file_accepted(self, file_path: str) -> bool:
        """فحص ما إذا كان الملف مقبولاً"""
        if not self.accepted_extensions:
            return True  # قبول جميع الملفات
            
        file_ext = os.path.splitext(file_path)[1].lower()
        return file_ext in self.accepted_extensions
        
    def browse_files(self):
        """تصفح الملفات"""
        if self.accepted_extensions:
            filter_text = f"الملفات المدعومة ({' '.join(['*' + ext for ext in self.accepted_extensions])})"
        else:
            filter_text = "جميع الملفات (*)"
            
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            self,
            "اختر الملفات",
            "",
            f"{filter_text};;جميع الملفات (*)"
        )
        
        if file_paths:
            self.files_dropped.emit(file_paths)


class FileListWidget(QListWidget):
    """قائمة الملفات المحملة"""
    
    file_selected = pyqtSignal(str)  # file_path
    file_removed = pyqtSignal(str)  # file_path
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.file_info_dict = {}
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setAlternatingRowColors(True)
        self.setStyleSheet(f"""
            QListWidget {{
                background-color: {ModernStyleManager.COLORS['surface']};
                border: 1px solid {ModernStyleManager.COLORS['border_light']};
                border-radius: 8px;
                padding: 4px;
            }}
            QListWidget::item {{
                padding: 8px;
                border-radius: 4px;
                margin: 2px;
            }}
            QListWidget::item:selected {{
                background-color: {ModernStyleManager.COLORS['selected']};
                color: {ModernStyleManager.COLORS['text_light']};
            }}
            QListWidget::item:hover {{
                background-color: {ModernStyleManager.COLORS['card']};
            }}
        """)
        
        # الاتصالات
        self.itemClicked.connect(self.on_item_clicked)
        
    def add_file(self, file_path: str, file_info: Dict):
        """إضافة ملف للقائمة"""
        self.file_info_dict[file_path] = file_info
        
        # إنشاء عنصر القائمة
        item = QListWidgetItem()
        
        # تحديد الأيقونة حسب نوع الملف
        file_ext = file_info.get('extension', '').lower()
        if file_ext in ['.obj', '.stl', '.dae', '.ply']:
            icon = IconsManager.get_standard_icon('furniture')
        elif file_ext in ['.xlsx', '.xls']:
            icon = IconsManager.get_standard_icon('table')
        elif file_ext in ['.pdf']:
            icon = IconsManager.get_standard_icon('report')
        else:
            icon = IconsManager.get_standard_icon('new')
            
        item.setIcon(icon)
        
        # تعيين النص
        file_name = file_info.get('name', os.path.basename(file_path))
        file_size = file_info.get('size', 0)
        size_text = self.format_file_size(file_size)
        
        item.setText(f"{file_name}\n{size_text}")
        item.setData(Qt.ItemDataRole.UserRole, file_path)
        
        # إضافة للقائمة
        self.addItem(item)
        
    def remove_file(self, file_path: str):
        """إزالة ملف من القائمة"""
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == file_path:
                self.takeItem(i)
                if file_path in self.file_info_dict:
                    del self.file_info_dict[file_path]
                self.file_removed.emit(file_path)
                break
                
    def clear_files(self):
        """مسح جميع الملفات"""
        self.clear()
        self.file_info_dict.clear()
        
    def get_file_info(self, file_path: str) -> Optional[Dict]:
        """الحصول على معلومات الملف"""
        return self.file_info_dict.get(file_path)
        
    def get_all_files(self) -> List[str]:
        """الحصول على جميع مسارات الملفات"""
        return list(self.file_info_dict.keys())
        
    def on_item_clicked(self, item: QListWidgetItem):
        """عند النقر على عنصر"""
        file_path = item.data(Qt.ItemDataRole.UserRole)
        if file_path:
            self.file_selected.emit(file_path)
            
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """تنسيق حجم الملف"""
        if size_bytes < 1024:
            return f"{size_bytes} بايت"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


class DragDropFileManager(QWidget):
    """مدير السحب والإفلات الشامل"""
    
    file_loaded = pyqtSignal(str, dict)  # file_path, file_info
    files_processed = pyqtSignal(list)  # List[Dict] - processed files info
    
    def __init__(self, accepted_extensions: List[str] = None, parent=None):
        super().__init__(parent)
        self.accepted_extensions = accepted_extensions or []
        self.file_handlers = {}
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # منطقة السحب والإفلات
        self.drop_zone = DropZoneWidget(self.accepted_extensions)
        self.drop_zone.files_dropped.connect(self.process_files)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # قائمة الملفات
        files_group = QGroupBox("الملفات المحملة")
        files_layout = QVBoxLayout(files_group)
        
        self.file_list = FileListWidget()
        self.file_list.file_selected.connect(self.on_file_selected)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        clear_btn = QPushButton("مسح الكل")
        clear_btn.setIcon(IconsManager.get_standard_icon('delete'))
        clear_btn.clicked.connect(self.clear_all_files)
        
        process_btn = QPushButton("معالجة الملفات")
        process_btn.setIcon(IconsManager.get_standard_icon('tools'))
        process_btn.clicked.connect(self.process_all_files)
        
        buttons_layout.addWidget(clear_btn)
        buttons_layout.addWidget(process_btn)
        buttons_layout.addStretch()
        
        files_layout.addWidget(self.file_list)
        files_layout.addLayout(buttons_layout)
        
        # إضافة العناصر للتخطيط الرئيسي
        layout.addWidget(self.drop_zone)
        layout.addWidget(self.progress_bar)
        layout.addWidget(files_group)
        
    def register_file_handler(self, extension: str, handler: Callable):
        """تسجيل معالج لنوع ملف معين"""
        self.file_handlers[extension.lower()] = handler
        
    def process_files(self, file_paths: List[str]):
        """معالجة الملفات المحددة"""
        if not file_paths:
            return
            
        # إظهار شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # إنشاء معالج الملفات
        self.processor = FileProcessor(file_paths, self.file_handlers)
        self.processor.file_processed.connect(self.on_file_processed)
        self.processor.processing_progress.connect(self.update_progress)
        self.processor.processing_error.connect(self.on_processing_error)
        self.processor.finished.connect(self.on_processing_finished)
        self.processor.start()
        
    def on_file_processed(self, file_path: str, file_info: Dict):
        """عند معالجة ملف"""
        self.file_list.add_file(file_path, file_info)
        self.file_loaded.emit(file_path, file_info)
        
    def update_progress(self, value: int, message: str):
        """تحديث شريط التقدم"""
        self.progress_bar.setValue(value)
        
    def on_processing_error(self, file_path: str, error_message: str):
        """عند حدوث خطأ في المعالجة"""
        QMessageBox.warning(self, "خطأ في المعالجة", 
                          f"خطأ في معالجة الملف:\n{os.path.basename(file_path)}\n\n{error_message}")
        
    def on_processing_finished(self):
        """عند انتهاء المعالجة"""
        self.progress_bar.setVisible(False)
        
        # إرسال إشارة بجميع الملفات المعالجة
        all_files_info = []
        for file_path in self.file_list.get_all_files():
            file_info = self.file_list.get_file_info(file_path)
            if file_info:
                all_files_info.append(file_info)
                
        self.files_processed.emit(all_files_info)
        
    def on_file_selected(self, file_path: str):
        """عند تحديد ملف"""
        file_info = self.file_list.get_file_info(file_path)
        if file_info:
            self.file_loaded.emit(file_path, file_info)
            
    def clear_all_files(self):
        """مسح جميع الملفات"""
        self.file_list.clear_files()
        
    def process_all_files(self):
        """معالجة جميع الملفات"""
        file_paths = self.file_list.get_all_files()
        if file_paths:
            self.process_files(file_paths)
        else:
            QMessageBox.information(self, "معلومات", "لا توجد ملفات للمعالجة")
