#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المخزون المكتمل لتطبيق تصميم الأثاث
Complete Inventory Management System for Furniture Design Application
"""

import os
import json
import sqlite3
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QDialog, QFormLayout, QDialogButtonBox, QGroupBox,
    QCheckBox, QDoubleSpinBox, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, Q<PERSON>rame
)
from PyQt6.QtCore import Qt, QDate, Q<PERSON>hr<PERSON>, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QPixmap, QPainter

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class InventoryItem:
    """عنصر المخزون"""
    id: int
    name: str
    category: str
    description: str
    unit: str  # متر، قطعة، كيلو، إلخ
    current_stock: float
    min_stock: float
    max_stock: float
    unit_cost: float
    selling_price: float
    supplier: str
    location: str
    barcode: str
    status: str  # available, low_stock, out_of_stock, discontinued
    last_updated: str
    notes: str

    def __post_init__(self):
        if not self.last_updated:
            self.last_updated = datetime.now().isoformat()


@dataclass
class StockMovement:
    """حركة المخزون"""
    id: int
    item_id: int
    movement_type: str  # in, out, adjustment
    quantity: float
    unit_cost: float
    total_cost: float
    reference: str  # رقم المرجع (فاتورة، طلب، إلخ)
    notes: str
    created_date: str
    created_by: str


class InventoryItemDialog(QDialog):
    """نافذة إضافة/تعديل عنصر المخزون"""

    def __init__(self, item: Optional[InventoryItem] = None, parent=None):
        super().__init__(parent)
        self.item = item
        self.is_edit_mode = item is not None
        self.init_ui()
        if self.item:
            self.load_item_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تعديل العنصر" if self.is_edit_mode else "إضافة عنصر جديد")
        self.setModal(True)
        self.resize(500, 600)

        layout = QVBoxLayout(self)

        # تبويبات البيانات
        tabs = QTabWidget()

        # تبويب البيانات الأساسية
        basic_tab = self.create_basic_info_tab()
        tabs.addTab(basic_tab, "البيانات الأساسية")

        # تبويب المخزون والأسعار
        stock_tab = self.create_stock_info_tab()
        tabs.addTab(stock_tab, "المخزون والأسعار")

        # تبويب الملاحظات
        notes_tab = self.create_notes_tab()
        tabs.addTab(notes_tab, "الملاحظات")

        layout.addWidget(tabs)

        # أزرار التحكم
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص الأزرار
        ok_button = buttons.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setText("حفظ")
        ok_button.setIcon(IconsManager.get_standard_icon('save'))

        cancel_button = buttons.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setText("إلغاء")
        cancel_button.setIcon(IconsManager.get_standard_icon('close'))

        layout.addWidget(buttons)

    def create_basic_info_tab(self):
        """إنشاء تبويب البيانات الأساسية"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(ModernStyleManager.SPACING['md'])

        # اسم العنصر
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم العنصر")
        layout.addRow("اسم العنصر *:", self.name_edit)

        # الفئة
        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)
        self.category_combo.addItems([
            "خشب", "معادن", "أقمشة", "إكسسوارات", "مسامير وبراغي",
            "دهانات", "لواصق", "أدوات", "مواد تشطيب", "أخرى"
        ])
        layout.addRow("الفئة:", self.category_combo)

        # الوصف
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("وصف العنصر")
        layout.addRow("الوصف:", self.description_edit)

        # الوحدة
        self.unit_combo = QComboBox()
        self.unit_combo.addItems([
            "متر", "متر مربع", "متر مكعب", "قطعة", "كيلو", "لتر", "علبة", "رزمة"
        ])
        layout.addRow("الوحدة:", self.unit_combo)

        # المورد
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setPlaceholderText("اسم المورد")
        layout.addRow("المورد:", self.supplier_edit)

        # الموقع
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("موقع التخزين")
        layout.addRow("الموقع:", self.location_edit)

        # الباركود
        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText("رقم الباركود")
        layout.addRow("الباركود:", self.barcode_edit)

        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["متاح", "مخزون منخفض", "نفد المخزون", "متوقف"])
        layout.addRow("الحالة:", self.status_combo)

        return tab

    def create_stock_info_tab(self):
        """إنشاء تبويب المخزون والأسعار"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(ModernStyleManager.SPACING['md'])

        # الكمية الحالية
        self.current_stock_spin = QDoubleSpinBox()
        self.current_stock_spin.setRange(0, 999999)
        self.current_stock_spin.setDecimals(2)
        layout.addRow("الكمية الحالية:", self.current_stock_spin)

        # الحد الأدنى
        self.min_stock_spin = QDoubleSpinBox()
        self.min_stock_spin.setRange(0, 999999)
        self.min_stock_spin.setDecimals(2)
        layout.addRow("الحد الأدنى:", self.min_stock_spin)

        # الحد الأقصى
        self.max_stock_spin = QDoubleSpinBox()
        self.max_stock_spin.setRange(0, 999999)
        self.max_stock_spin.setDecimals(2)
        layout.addRow("الحد الأقصى:", self.max_stock_spin)

        # تكلفة الوحدة
        self.unit_cost_spin = QDoubleSpinBox()
        self.unit_cost_spin.setRange(0, 999999)
        self.unit_cost_spin.setDecimals(2)
        self.unit_cost_spin.setSuffix(" ريال")
        layout.addRow("تكلفة الوحدة:", self.unit_cost_spin)

        # سعر البيع
        self.selling_price_spin = QDoubleSpinBox()
        self.selling_price_spin.setRange(0, 999999)
        self.selling_price_spin.setDecimals(2)
        self.selling_price_spin.setSuffix(" ريال")
        layout.addRow("سعر البيع:", self.selling_price_spin)

        return tab

    def create_notes_tab(self):
        """إنشاء تبويب الملاحظات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية حول العنصر...")
        layout.addWidget(self.notes_edit)

        return tab

    def load_item_data(self):
        """تحميل بيانات العنصر للتعديل"""
        if not self.item:
            return

        self.name_edit.setText(self.item.name)
        self.description_edit.setPlainText(self.item.description)
        self.supplier_edit.setText(self.item.supplier)
        self.location_edit.setText(self.item.location)
        self.barcode_edit.setText(self.item.barcode)
        self.notes_edit.setPlainText(self.item.notes)

        # تعيين القوائم المنسدلة
        category_index = self.category_combo.findText(self.item.category)
        if category_index >= 0:
            self.category_combo.setCurrentIndex(category_index)
        else:
            self.category_combo.setCurrentText(self.item.category)

        unit_index = self.unit_combo.findText(self.item.unit)
        if unit_index >= 0:
            self.unit_combo.setCurrentIndex(unit_index)

        statuses = {"available": 0, "low_stock": 1, "out_of_stock": 2, "discontinued": 3}
        self.status_combo.setCurrentIndex(statuses.get(self.item.status, 0))

        # تعيين القيم الرقمية
        self.current_stock_spin.setValue(self.item.current_stock)
        self.min_stock_spin.setValue(self.item.min_stock)
        self.max_stock_spin.setValue(self.item.max_stock)
        self.unit_cost_spin.setValue(self.item.unit_cost)
        self.selling_price_spin.setValue(self.item.selling_price)

    def get_item_data(self) -> InventoryItem:
        """الحصول على بيانات العنصر من النموذج"""
        statuses = ["available", "low_stock", "out_of_stock", "discontinued"]

        return InventoryItem(
            id=self.item.id if self.item else 0,
            name=self.name_edit.text().strip(),
            category=self.category_combo.currentText().strip(),
            description=self.description_edit.toPlainText().strip(),
            unit=self.unit_combo.currentText(),
            current_stock=self.current_stock_spin.value(),
            min_stock=self.min_stock_spin.value(),
            max_stock=self.max_stock_spin.value(),
            unit_cost=self.unit_cost_spin.value(),
            selling_price=self.selling_price_spin.value(),
            supplier=self.supplier_edit.text().strip(),
            location=self.location_edit.text().strip(),
            barcode=self.barcode_edit.text().strip(),
            status=statuses[self.status_combo.currentIndex()],
            last_updated=datetime.now().isoformat(),
            notes=self.notes_edit.toPlainText().strip()
        )

    def validate_data(self) -> bool:
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العنصر")
            self.name_edit.setFocus()
            return False

        if self.min_stock_spin.value() > self.max_stock_spin.value():
            QMessageBox.warning(self, "خطأ", "الحد الأدنى لا يمكن أن يكون أكبر من الحد الأقصى")
            self.min_stock_spin.setFocus()
            return False

        return True

    def accept(self):
        """قبول النموذج"""
        if self.validate_data():
            super().accept()


class InventoryDatabase:
    """قاعدة بيانات المخزون"""

    def __init__(self, db_path: str = "inventory.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # جدول عناصر المخزون
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS inventory_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    category TEXT,
                    description TEXT,
                    unit TEXT,
                    current_stock REAL,
                    min_stock REAL,
                    max_stock REAL,
                    unit_cost REAL,
                    selling_price REAL,
                    supplier TEXT,
                    location TEXT,
                    barcode TEXT,
                    status TEXT,
                    last_updated TEXT,
                    notes TEXT
                )
            """)

            # جدول حركات المخزون
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS stock_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_id INTEGER,
                    movement_type TEXT,
                    quantity REAL,
                    unit_cost REAL,
                    total_cost REAL,
                    reference TEXT,
                    notes TEXT,
                    created_date TEXT,
                    created_by TEXT,
                    FOREIGN KEY (item_id) REFERENCES inventory_items (id)
                )
            """)

            conn.commit()

    def add_item(self, item: InventoryItem) -> int:
        """إضافة عنصر جديد"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO inventory_items (
                    name, category, description, unit, current_stock, min_stock,
                    max_stock, unit_cost, selling_price, supplier, location,
                    barcode, status, last_updated, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                item.name, item.category, item.description, item.unit,
                item.current_stock, item.min_stock, item.max_stock,
                item.unit_cost, item.selling_price, item.supplier,
                item.location, item.barcode, item.status,
                item.last_updated, item.notes
            ))
            return cursor.lastrowid

    def update_item(self, item: InventoryItem):
        """تحديث بيانات العنصر"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE inventory_items SET
                    name=?, category=?, description=?, unit=?, current_stock=?,
                    min_stock=?, max_stock=?, unit_cost=?, selling_price=?,
                    supplier=?, location=?, barcode=?, status=?, last_updated=?, notes=?
                WHERE id=?
            """, (
                item.name, item.category, item.description, item.unit,
                item.current_stock, item.min_stock, item.max_stock,
                item.unit_cost, item.selling_price, item.supplier,
                item.location, item.barcode, item.status,
                item.last_updated, item.notes, item.id
            ))

    def delete_item(self, item_id: int):
        """حذف عنصر"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM inventory_items WHERE id=?", (item_id,))
            cursor.execute("DELETE FROM stock_movements WHERE item_id=?", (item_id,))

    def get_all_items(self) -> List[InventoryItem]:
        """الحصول على جميع العناصر"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM inventory_items ORDER BY name")
            rows = cursor.fetchall()

            items = []
            for row in rows:
                item = InventoryItem(
                    id=row[0], name=row[1], category=row[2], description=row[3],
                    unit=row[4], current_stock=row[5], min_stock=row[6],
                    max_stock=row[7], unit_cost=row[8], selling_price=row[9],
                    supplier=row[10], location=row[11], barcode=row[12],
                    status=row[13], last_updated=row[14], notes=row[15]
                )
                items.append(item)

            return items

    def search_items(self, search_term: str) -> List[InventoryItem]:
        """البحث في العناصر"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM inventory_items
                WHERE name LIKE ? OR category LIKE ? OR supplier LIKE ? OR barcode LIKE ?
                ORDER BY name
            """, (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))

            rows = cursor.fetchall()
            items = []
            for row in rows:
                item = InventoryItem(
                    id=row[0], name=row[1], category=row[2], description=row[3],
                    unit=row[4], current_stock=row[5], min_stock=row[6],
                    max_stock=row[7], unit_cost=row[8], selling_price=row[9],
                    supplier=row[10], location=row[11], barcode=row[12],
                    status=row[13], last_updated=row[14], notes=row[15]
                )
                items.append(item)

            return items

    def get_low_stock_items(self) -> List[InventoryItem]:
        """الحصول على العناصر ذات المخزون المنخفض"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM inventory_items
                WHERE current_stock <= min_stock AND status != 'discontinued'
                ORDER BY (current_stock / min_stock) ASC
            """)

            rows = cursor.fetchall()
            items = []
            for row in rows:
                item = InventoryItem(
                    id=row[0], name=row[1], category=row[2], description=row[3],
                    unit=row[4], current_stock=row[5], min_stock=row[6],
                    max_stock=row[7], unit_cost=row[8], selling_price=row[9],
                    supplier=row[10], location=row[11], barcode=row[12],
                    status=row[13], last_updated=row[14], notes=row[15]
                )
                items.append(item)

            return items

    def add_stock_movement(self, movement: StockMovement) -> int:
        """إضافة حركة مخزون"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO stock_movements (
                    item_id, movement_type, quantity, unit_cost, total_cost,
                    reference, notes, created_date, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                movement.item_id, movement.movement_type, movement.quantity,
                movement.unit_cost, movement.total_cost, movement.reference,
                movement.notes, movement.created_date, movement.created_by
            ))

            # تحديث المخزون
            if movement.movement_type == "in":
                cursor.execute("""
                    UPDATE inventory_items
                    SET current_stock = current_stock + ?, last_updated = ?
                    WHERE id = ?
                """, (movement.quantity, datetime.now().isoformat(), movement.item_id))
            elif movement.movement_type == "out":
                cursor.execute("""
                    UPDATE inventory_items
                    SET current_stock = current_stock - ?, last_updated = ?
                    WHERE id = ?
                """, (movement.quantity, datetime.now().isoformat(), movement.item_id))
            elif movement.movement_type == "adjustment":
                cursor.execute("""
                    UPDATE inventory_items
                    SET current_stock = ?, last_updated = ?
                    WHERE id = ?
                """, (movement.quantity, datetime.now().isoformat(), movement.item_id))

            return cursor.lastrowid


class InventoryManagerWidget(QWidget):
    """واجهة إدارة المخزون الرئيسية"""

    def __init__(self):
        super().__init__()
        self.db = InventoryDatabase()
        self.items = []
        self.selected_item = None
        self.init_ui()
        self.load_items()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(ModernStyleManager.SPACING['md'])
        layout.setContentsMargins(
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg']
        )

        # شريط الأدوات العلوي
        toolbar_layout = QHBoxLayout()

        # عنوان الصفحة
        title_label = QLabel("📦 إدارة المخزون")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                margin-bottom: {ModernStyleManager.SPACING['md']}px;
            }}
        """)

        # شريط البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔍 البحث في المخزون...")
        self.search_edit.textChanged.connect(self.search_items)
        self.search_edit.setProperty("inputSize", "medium")

        # فلتر الفئة
        self.category_filter = QComboBox()
        self.category_filter.addItem("جميع الفئات")
        self.category_filter.currentTextChanged.connect(self.filter_by_category)

        # أزرار التحكم
        self.add_item_btn = QPushButton("إضافة عنصر")
        self.add_item_btn.setIcon(IconsManager.get_standard_icon('add'))
        self.add_item_btn.clicked.connect(self.add_item)
        self.add_item_btn.setProperty("buttonSize", "medium")

        self.edit_item_btn = QPushButton("تعديل")
        self.edit_item_btn.setIcon(IconsManager.get_standard_icon('edit'))
        self.edit_item_btn.clicked.connect(self.edit_item)
        self.edit_item_btn.setEnabled(False)
        self.edit_item_btn.setProperty("buttonSize", "medium")

        self.delete_item_btn = QPushButton("حذف")
        self.delete_item_btn.setIcon(IconsManager.get_standard_icon('delete'))
        self.delete_item_btn.clicked.connect(self.delete_item)
        self.delete_item_btn.setEnabled(False)
        self.delete_item_btn.setProperty("buttonSize", "medium")

        self.low_stock_btn = QPushButton("مخزون منخفض")
        self.low_stock_btn.setIcon(IconsManager.get_standard_icon('warning'))
        self.low_stock_btn.clicked.connect(self.show_low_stock)
        self.low_stock_btn.setProperty("buttonSize", "medium")

        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.setIcon(IconsManager.get_standard_icon('refresh'))
        self.refresh_btn.clicked.connect(self.load_items)
        self.refresh_btn.setProperty("buttonSize", "medium")

        toolbar_layout.addWidget(title_label)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(self.category_filter)
        toolbar_layout.addSpacing(ModernStyleManager.SPACING['md'])
        toolbar_layout.addWidget(self.add_item_btn)
        toolbar_layout.addWidget(self.edit_item_btn)
        toolbar_layout.addWidget(self.delete_item_btn)
        toolbar_layout.addWidget(self.low_stock_btn)
        toolbar_layout.addWidget(self.refresh_btn)

        layout.addLayout(toolbar_layout)

        # المقسم الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # جدول العناصر
        self.create_items_table(main_splitter)

        # لوحة تفاصيل العنصر
        self.create_item_details_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([700, 300])

        layout.addWidget(main_splitter)

        # شريط الحالة
        status_layout = QHBoxLayout()

        self.status_label = QLabel("جاهز")
        self.items_count_label = QLabel("العناصر: 0")
        self.total_value_label = QLabel("القيمة الإجمالية: 0 ريال")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.items_count_label)
        status_layout.addWidget(self.total_value_label)

        layout.addLayout(status_layout)
