#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي لمصمم الأثاث الاحترافي - PyQt6
Professional Furniture Designer Main Application - PyQt6
"""

import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QSplitter, QTabWidget,
    QTreeWidget, QTreeWidgetItem, QTableWidget, QLabel,
    QPushButton, QMessageBox, QFileDialog, QProgressBar
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QFont, QPixmap

# استيراد الوحدات المحلية
from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager
from models.advanced_3d_viewer import Advanced3DViewer
from drag_drop.file_handler import DragDropFileManager
from cnc.cnc_integration import CNCMachineManager
from reports.reports_widget import AdvancedReportsWidget
from backup.backup_manager import BackupManager
from clients.client_manager import ClientManagerWidget
from inventory.inventory_manager import InventoryManagerWidget


class SplashScreen(QWidget):
    """شاشة البداية الاحترافية"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة شاشة البداية"""
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setFixedSize(600, 400)

        # تخطيط رئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء الخلفية
        self.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernStyleManager.COLORS['primary']},
                    stop:1 {ModernStyleManager.COLORS['secondary']});
                border-radius: 20px;
                color: {ModernStyleManager.COLORS['text_light']};
            }}
        """)

        # العنوان الرئيسي
        title_label = QLabel("🪑 مصمم الأثاث الاحترافي")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                margin: 50px 0 20px 0;
                background: transparent;
            }}
        """)

        # العنوان الفرعي
        subtitle_label = QLabel("Professional Furniture Design Suite")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['subheading']}px;
                margin-bottom: 30px;
                background: transparent;
                color: {ModernStyleManager.COLORS['text_light']};
                opacity: 0.8;
            }}
        """)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {ModernStyleManager.COLORS['text_light']};
                border-radius: 10px;
                text-align: center;
                background-color: transparent;
                height: 20px;
                margin: 20px 50px;
            }}
            QProgressBar::chunk {{
                background-color: {ModernStyleManager.COLORS['text_light']};
                border-radius: 8px;
            }}
        """)

        # نص الحالة
        self.status_label = QLabel("جاري التحميل...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['body']}px;
                margin: 10px 0 50px 0;
                background: transparent;
            }}
        """)

        # إضافة العناصر للتخطيط
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        layout.addStretch()
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        layout.addStretch()

        # توسيط النافذة
        self.center_on_screen()

    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)

    def update_progress(self, value: int, message: str):
        """تحديث شريط التقدم والرسالة"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        QApplication.processEvents()


class LoadingThread(QThread):
    """خيط التحميل"""
    progress_updated = pyqtSignal(int, str)
    loading_finished = pyqtSignal()

    def run(self):
        """تشغيل عملية التحميل"""
        steps = [
            (10, "تهيئة قواعد البيانات..."),
            (25, "تحميل إعدادات التطبيق..."),
            (40, "تهيئة واجهة المستخدم..."),
            (60, "تحميل الأنماط والأيقونات..."),
            (80, "تهيئة الوحدات المتقدمة..."),
            (95, "إنهاء التحميل..."),
            (100, "مرحباً بك!")
        ]

        for progress, message in steps:
            self.progress_updated.emit(progress, message)
            self.msleep(500)  # محاكاة وقت التحميل

        self.loading_finished.emit()


class ModernMainWindow(QMainWindow):
    """النافذة الرئيسية الحديثة"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مصمم الأثاث الاحترافي - Professional Furniture Designer")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # إعداد الأيقونة
        self.setWindowIcon(IconsManager.get_standard_icon('furniture'))

        # إنشاء القوائم
        self.create_menus()

        # إنشاء شريط الأدوات
        self.create_toolbars()

        # إنشاء المحتوى الرئيسي
        self.create_main_content()

        # إنشاء شريط الحالة
        self.create_status_bar()

        # توسيط النافذة
        self.center_on_screen()

    def create_menus(self):
        """إنشاء القوائم"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu("ملف")

        new_action = QAction(IconsManager.get_action_icon('new_project'), "مشروع جديد", self)
        new_action.setShortcut("Ctrl+N")
        new_action.setStatusTip("إنشاء مشروع أثاث جديد")
        file_menu.addAction(new_action)

        open_action = QAction(IconsManager.get_action_icon('open_project'), "فتح مشروع", self)
        open_action.setShortcut("Ctrl+O")
        open_action.setStatusTip("فتح مشروع موجود")
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        save_action = QAction(IconsManager.get_action_icon('save_project'), "حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.setStatusTip("حفظ المشروع الحالي")
        file_menu.addAction(save_action)

        # قائمة التحرير
        edit_menu = menubar.addMenu("تحرير")

        add_component_action = QAction(IconsManager.get_action_icon('add_component'), "إضافة مكون", self)
        add_component_action.setStatusTip("إضافة مكون جديد للمشروع")
        edit_menu.addAction(add_component_action)

        # قائمة العرض
        view_menu = menubar.addMenu("عرض")

        # قائمة الأدوات
        tools_menu = menubar.addMenu("أدوات")

        optimize_action = QAction(IconsManager.get_action_icon('optimize_cutting'), "تحسين القطع", self)
        optimize_action.setStatusTip("تحسين خطة قطع المواد")
        tools_menu.addAction(optimize_action)

        tools_menu.addSeparator()

        cnc_action = QAction(IconsManager.get_standard_icon('tools'), "إدارة آلات CNC", self)
        cnc_action.setStatusTip("إدارة وتشغيل آلات CNC")
        cnc_action.triggered.connect(self.show_cnc_manager)
        tools_menu.addAction(cnc_action)

        backup_action = QAction(IconsManager.get_standard_icon('database'), "النسخ الاحتياطي", self)
        backup_action.setStatusTip("إدارة النسخ الاحتياطي للبيانات")
        backup_action.triggered.connect(self.show_backup_manager)
        tools_menu.addAction(backup_action)

        # قائمة التقارير
        reports_menu = menubar.addMenu("تقارير")

        advanced_reports_action = QAction(IconsManager.get_action_icon('generate_report'), "التقارير المتقدمة", self)
        advanced_reports_action.setStatusTip("إنشاء تقارير متقدمة ومفصلة")
        advanced_reports_action.triggered.connect(self.show_advanced_reports)
        reports_menu.addAction(advanced_reports_action)

        financial_report_action = QAction(IconsManager.get_action_icon('financial_report'), "التقرير المالي", self)
        financial_report_action.setStatusTip("عرض التقرير المالي")
        reports_menu.addAction(financial_report_action)

        inventory_report_action = QAction(IconsManager.get_standard_icon('database'), "تقرير المخزون", self)
        inventory_report_action.setStatusTip("عرض تقرير المخزون")
        reports_menu.addAction(inventory_report_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")

        about_action = QAction(IconsManager.get_action_icon('about'), "حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbars(self):
        """إنشاء أشرطة الأدوات"""
        # شريط الأدوات الرئيسي
        main_toolbar = self.addToolBar("الأدوات الرئيسية")
        main_toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)

        # أزرار الملفات
        main_toolbar.addAction(IconsManager.get_action_icon('new_project'), "جديد")
        main_toolbar.addAction(IconsManager.get_action_icon('open_project'), "فتح")
        main_toolbar.addAction(IconsManager.get_action_icon('save_project'), "حفظ")

        main_toolbar.addSeparator()

        # أزرار التصميم
        main_toolbar.addAction(IconsManager.get_action_icon('add_component'), "إضافة مكون")
        main_toolbar.addAction(IconsManager.get_action_icon('optimize_cutting'), "تحسين القطع")

        main_toolbar.addSeparator()

        # أزرار التقارير
        main_toolbar.addAction(IconsManager.get_action_icon('generate_report'), "تقرير")
        main_toolbar.addAction(IconsManager.get_action_icon('export_excel'), "تصدير Excel")

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # تخطيط رئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(8)

        # إنشاء المقسم الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # الشريط الجانبي الأيسر
        self.create_sidebar(main_splitter)

        # المنطقة الوسطى
        self.create_center_area(main_splitter)

        # الشريط الجانبي الأيمن
        self.create_right_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([250, 700, 250])

        main_layout.addWidget(main_splitter)

    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar = QWidget()
        sidebar.setMaximumWidth(300)
        sidebar.setMinimumWidth(200)

        layout = QVBoxLayout(sidebar)

        # عنوان الشريط الجانبي
        title_label = QLabel("مستكشف المشروع")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                padding: 12px;
                background-color: {ModernStyleManager.COLORS['primary']};
                color: {ModernStyleManager.COLORS['text_light']};
                border-radius: 8px;
                margin-bottom: 8px;
            }}
        """)

        # شجرة المشروع
        self.project_tree = QTreeWidget()
        self.project_tree.setHeaderLabel("المكونات")

        # إضافة عناصر تجريبية
        root_item = QTreeWidgetItem(["المشروع الحالي"])
        root_item.setIcon(0, IconsManager.get_standard_icon('furniture'))

        components_item = QTreeWidgetItem(["المكونات"])
        components_item.setIcon(0, IconsManager.get_standard_icon('table'))

        materials_item = QTreeWidgetItem(["المواد"])
        materials_item.setIcon(0, IconsManager.get_standard_icon('database'))

        root_item.addChild(components_item)
        root_item.addChild(materials_item)

        self.project_tree.addTopLevelItem(root_item)
        self.project_tree.expandAll()

        layout.addWidget(title_label)
        layout.addWidget(self.project_tree)

        parent.addWidget(sidebar)

    def create_center_area(self, parent):
        """إنشاء المنطقة الوسطى"""
        center_widget = QWidget()
        layout = QVBoxLayout(center_widget)

        # تبويبات المحتوى
        self.content_tabs = QTabWidget()

        # تبويب التصميم
        design_tab = QWidget()
        design_layout = QVBoxLayout(design_tab)

        welcome_label = QLabel("مرحباً بك في مصمم الأثاث الاحترافي")
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        welcome_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                margin: 50px;
            }}
        """)

        design_layout.addWidget(welcome_label)

        self.content_tabs.addTab(design_tab, "التصميم")

        # تبويب المعاينة ثلاثية الأبعاد
        try:
            self.viewer_3d = Advanced3DViewer()
            self.content_tabs.addTab(self.viewer_3d, "المعاينة ثلاثية الأبعاد")
        except Exception as e:
            print(f"⚠️  تعذر تحميل المعاين ثلاثي الأبعاد: {e}")
            # إنشاء تبويب بديل
            viewer_placeholder = QWidget()
            viewer_layout = QVBoxLayout(viewer_placeholder)
            viewer_label = QLabel("⚠️ المعاين ثلاثي الأبعاد غير متاح\nيرجى تثبيت matplotlib و trimesh")
            viewer_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            viewer_label.setStyleSheet("color: #E74C3C; font-size: 16px; padding: 50px;")
            viewer_layout.addWidget(viewer_label)
            self.content_tabs.addTab(viewer_placeholder, "المعاينة ثلاثية الأبعاد")

        # تبويب السحب والإفلات
        try:
            self.drag_drop_manager = DragDropFileManager(['.obj', '.stl', '.dae', '.ply'])
            self.drag_drop_manager.file_loaded.connect(self.on_file_loaded)
            self.content_tabs.addTab(self.drag_drop_manager, "تحميل الملفات")
        except Exception as e:
            print(f"⚠️  تعذر تحميل مدير السحب والإفلات: {e}")
            # إنشاء تبويب بديل
            drag_placeholder = QWidget()
            drag_layout = QVBoxLayout(drag_placeholder)
            drag_label = QLabel("⚠️ نظام السحب والإفلات غير متاح")
            drag_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            drag_label.setStyleSheet("color: #E74C3C; font-size: 16px; padding: 50px;")
            drag_layout.addWidget(drag_label)
            self.content_tabs.addTab(drag_placeholder, "تحميل الملفات")

        # تبويب التقارير
        try:
            self.reports_widget = AdvancedReportsWidget()
            self.content_tabs.addTab(self.reports_widget, "التقارير")
        except Exception as e:
            print(f"⚠️  تعذر تحميل نظام التقارير: {e}")
            # إنشاء تبويب بديل
            reports_placeholder = QWidget()
            reports_layout = QVBoxLayout(reports_placeholder)
            reports_label = QLabel("⚠️ نظام التقارير غير متاح\nيرجى تثبيت pandas و matplotlib")
            reports_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            reports_label.setStyleSheet("color: #E74C3C; font-size: 16px; padding: 50px;")
            reports_layout.addWidget(reports_label)
            self.content_tabs.addTab(reports_placeholder, "التقارير")

        # تبويب إدارة CNC
        try:
            self.cnc_manager = CNCMachineManager()
            self.content_tabs.addTab(self.cnc_manager, "إدارة CNC")
        except Exception as e:
            print(f"⚠️  تعذر تحميل مدير CNC: {e}")
            # إنشاء تبويب بديل
            cnc_placeholder = QWidget()
            cnc_layout = QVBoxLayout(cnc_placeholder)
            cnc_label = QLabel("⚠️ مدير CNC غير متاح\nيرجى التحقق من المكتبات المطلوبة")
            cnc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            cnc_label.setStyleSheet("color: #E74C3C; font-size: 16px; padding: 50px;")
            cnc_layout.addWidget(cnc_label)
            self.content_tabs.addTab(cnc_placeholder, "إدارة CNC")

        # تبويب إدارة العملاء
        try:
            self.client_manager = ClientManagerWidget()
            self.content_tabs.addTab(self.client_manager, "إدارة العملاء")
        except Exception as e:
            print(f"⚠️  تعذر تحميل مدير العملاء: {e}")
            # إنشاء تبويب بديل
            client_placeholder = QWidget()
            client_layout = QVBoxLayout(client_placeholder)
            client_label = QLabel("⚠️ نظام إدارة العملاء غير متاح")
            client_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            client_label.setStyleSheet("color: #E74C3C; font-size: 16px; padding: 50px;")
            client_layout.addWidget(client_label)
            self.content_tabs.addTab(client_placeholder, "إدارة العملاء")

        # تبويب إدارة المخزون
        try:
            self.inventory_manager = InventoryManagerWidget()
            self.content_tabs.addTab(self.inventory_manager, "إدارة المخزون")
        except Exception as e:
            print(f"⚠️  تعذر تحميل مدير المخزون: {e}")
            # إنشاء تبويب بديل
            inventory_placeholder = QWidget()
            inventory_layout = QVBoxLayout(inventory_placeholder)
            inventory_label = QLabel("⚠️ نظام إدارة المخزون غير متاح")
            inventory_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            inventory_label.setStyleSheet("color: #E74C3C; font-size: 16px; padding: 50px;")
            inventory_layout.addWidget(inventory_label)
            self.content_tabs.addTab(inventory_placeholder, "إدارة المخزون")

        # تبويب النسخ الاحتياطي
        try:
            self.backup_manager = BackupManager()
            self.content_tabs.addTab(self.backup_manager, "النسخ الاحتياطي")
        except Exception as e:
            print(f"⚠️  تعذر تحميل مدير النسخ الاحتياطي: {e}")
            # إنشاء تبويب بديل
            backup_placeholder = QWidget()
            backup_layout = QVBoxLayout(backup_placeholder)
            backup_label = QLabel("⚠️ نظام النسخ الاحتياطي غير متاح")
            backup_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            backup_label.setStyleSheet("color: #E74C3C; font-size: 16px; padding: 50px;")
            backup_layout.addWidget(backup_label)
            self.content_tabs.addTab(backup_placeholder, "النسخ الاحتياطي")

        layout.addWidget(self.content_tabs)
        parent.addWidget(center_widget)

    def create_right_panel(self, parent):
        """إنشاء اللوحة اليمنى"""
        right_panel = QWidget()
        right_panel.setMaximumWidth(300)
        right_panel.setMinimumWidth(200)

        layout = QVBoxLayout(right_panel)

        # عنوان اللوحة
        title_label = QLabel("الخصائص والأدوات")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                padding: 12px;
                background-color: {ModernStyleManager.COLORS['secondary']};
                color: {ModernStyleManager.COLORS['text_light']};
                border-radius: 8px;
                margin-bottom: 8px;
            }}
        """)

        # أزرار سريعة
        quick_actions_label = QLabel("إجراءات سريعة")
        quick_actions_label.setStyleSheet(f"font-weight: bold; margin: 8px 0;")

        add_client_btn = QPushButton("إضافة عميل")
        add_client_btn.setIcon(IconsManager.get_action_icon('add_client'))

        manage_inventory_btn = QPushButton("إدارة المخزون")
        manage_inventory_btn.setIcon(IconsManager.get_action_icon('add_item'))

        generate_quote_btn = QPushButton("إنشاء عرض سعر")
        generate_quote_btn.setIcon(IconsManager.get_action_icon('generate_report'))

        layout.addWidget(title_label)
        layout.addWidget(quick_actions_label)
        layout.addWidget(add_client_btn)
        layout.addWidget(manage_inventory_btn)
        layout.addWidget(generate_quote_btn)
        layout.addStretch()

        parent.addWidget(right_panel)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()

        # رسالة الحالة
        status_bar.showMessage("جاهز - مرحباً بك في مصمم الأثاث الاحترافي")

        # معلومات إضافية
        self.user_label = QLabel("المستخدم: مدير النظام")
        self.project_label = QLabel("المشروع: غير محدد")

        status_bar.addPermanentWidget(self.user_label)
        status_bar.addPermanentWidget(self.project_label)

    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات السحب والإفلات مع المعاين ثلاثي الأبعاد
        if hasattr(self, 'drag_drop_manager') and hasattr(self.drag_drop_manager, 'file_loaded'):
            self.drag_drop_manager.file_loaded.connect(self.on_file_loaded)

        # ربط إشارات المعاين ثلاثي الأبعاد
        if hasattr(self, 'viewer_3d') and hasattr(self.viewer_3d, 'model_selected'):
            self.viewer_3d.model_selected.connect(self.on_model_selected)

    def on_file_loaded(self, file_path: str, file_info: dict):
        """عند تحميل ملف"""
        # إذا كان ملف نموذج ثلاثي الأبعاد، تحميله في المعاين
        if (file_info.get('extension', '').lower() in ['.obj', '.stl', '.dae', '.ply'] and
            hasattr(self, 'viewer_3d') and hasattr(self.viewer_3d, 'load_model_from_path')):
            self.viewer_3d.load_model_from_path(file_path)
            # التبديل إلى تبويب المعاينة ثلاثية الأبعاد
            self.content_tabs.setCurrentWidget(self.viewer_3d)

        # تحديث شريط الحالة
        self.statusBar().showMessage(f"تم تحميل الملف: {file_info.get('name', 'غير معروف')}")

    def on_model_selected(self, model_info):
        """عند تحديد نموذج ثلاثي الأبعاد"""
        # تحديث معلومات المشروع
        if hasattr(model_info, 'file_name'):
            self.project_label.setText(f"المشروع: {model_info.file_name}")

    def show_cnc_manager(self):
        """عرض مدير آلات CNC"""
        if hasattr(self, 'cnc_manager'):
            self.content_tabs.setCurrentWidget(self.cnc_manager)
        else:
            QMessageBox.warning(self, "تحذير", "مدير آلات CNC غير متاح")

    def show_backup_manager(self):
        """عرض مدير النسخ الاحتياطي"""
        if hasattr(self, 'backup_manager'):
            self.content_tabs.setCurrentWidget(self.backup_manager)
        else:
            QMessageBox.warning(self, "تحذير", "مدير النسخ الاحتياطي غير متاح")

    def show_advanced_reports(self):
        """عرض التقارير المتقدمة"""
        if hasattr(self, 'reports_widget'):
            self.content_tabs.setCurrentWidget(self.reports_widget)
        else:
            QMessageBox.warning(self, "تحذير", "نظام التقارير المتقدمة غير متاح")

    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(self, "حول البرنامج",
                         "🪑 مصمم الأثاث الاحترافي\n\n"
                         "الإصدار 3.0 - PyQt6 Edition\n"
                         "تطبيق شامل لتصميم الأثاث وإدارة مصانع الأثاث\n\n"
                         "الميزات الجديدة:\n"
                         "• واجهة مستخدم حديثة ومهنية\n"
                         "• معاينة ثلاثية الأبعاد متقدمة\n"
                         "• تكامل مع آلات CNC\n"
                         "• نظام إدارة شامل للعملاء والمخزون\n"
                         "• تقارير احترافية متقدمة\n\n"
                         "تطوير: فريق التطوير الاحترافي")


class FurnitureDesignerApp(QApplication):
    """تطبيق مصمم الأثاث الرئيسي"""

    def __init__(self, argv):
        super().__init__(argv)
        self.setup_application()
        self.show_splash_screen()

    def setup_application(self):
        """إعداد التطبيق"""
        # معلومات التطبيق
        self.setApplicationName("Professional Furniture Designer")
        self.setApplicationVersion("3.0")
        self.setOrganizationName("Professional Furniture Design")

        # إعداد الخط والأنماط
        ModernStyleManager.setup_application_font(self)
        self.setPalette(ModernStyleManager.create_color_palette())
        self.setStyleSheet(ModernStyleManager.get_complete_style())

        # التأكد من وجود المجلدات المطلوبة
        IconsManager.ensure_icons_directory()

    def show_splash_screen(self):
        """عرض شاشة البداية"""
        self.splash = SplashScreen()
        self.splash.show()

        # إنشاء خيط التحميل
        self.loading_thread = LoadingThread()
        self.loading_thread.progress_updated.connect(self.splash.update_progress)
        self.loading_thread.loading_finished.connect(self.show_main_window)
        self.loading_thread.start()

    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        self.splash.close()

        self.main_window = ModernMainWindow()
        self.main_window.show()


def main():
    """الدالة الرئيسية"""
    app = FurnitureDesignerApp(sys.argv)
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
